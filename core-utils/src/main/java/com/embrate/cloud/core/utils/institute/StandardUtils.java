package com.embrate.cloud.core.utils.institute;

import com.amazonaws.util.CollectionUtils;
import com.embrate.cloud.core.utils.EMapUtils;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.institute.Stream;
import org.apache.commons.lang3.StringUtils;


import java.util.*;

/**
 * <AUTHOR>
 * @created_at 19/09/24 : 12:30
 **/
public class StandardUtils {

    public static final String COMMA_DELIMITER = ",";
    public static final String COLON_DELIMITER = ":";

    public static void getStandardSectionSet(String standardIdsCsv, Set<UUID> standardIdSet, Set<Integer> sectionIdSet) {
        if (!StringUtils.isBlank(standardIdsCsv)) {
            final String[] requiredStandardsArray = standardIdsCsv.split(COMMA_DELIMITER);
            for (final String standardIdStr : requiredStandardsArray) {
                if (!standardIdStr.contains(COLON_DELIMITER)) {
                    UUID standardId = UUID.fromString(standardIdStr);
                    standardIdSet.add(standardId);
                    continue;
                }
                String[] standardSectionToken = standardIdStr.split(COLON_DELIMITER);
                UUID standardId = UUID.fromString(standardSectionToken[0]);
                Integer sectionId = Integer.parseInt(standardSectionToken[1]);
                standardIdSet.add(standardId);
                sectionIdSet.add(sectionId);
            }
        }
    }

    public static void getStandardSet(String standardIdsCsv, Set<UUID> standardIds) {
        if (StringUtils.isNotBlank(standardIdsCsv)) {
            final String[] standardsTokens = standardIdsCsv.split(COMMA_DELIMITER);
            for (final String standardId : standardsTokens) {
                standardIds.add(UUID.fromString(standardId));
            }
        }
    }

    public static Map<UUID, List<Integer>> convertToRequiredStandardsWithSection(String requiredStandardsCSV) {
        final Map<UUID, List<Integer>> requiredStandards = new HashMap<UUID, List<Integer>>();
        if (StringUtils.isBlank(requiredStandardsCSV)) {
            return requiredStandards;
        }
        final String[] requiredStandardsArray = requiredStandardsCSV.split(COMMA_DELIMITER);
        for (final String requiredStandard : requiredStandardsArray) {
            final String[] requiredSectionsArray = requiredStandard.split(COLON_DELIMITER);
            if (requiredSectionsArray.length == 2) {
                UUID standardId = UUID.fromString(requiredSectionsArray[0].trim());
                int sectionId = Integer.valueOf(requiredSectionsArray[1].trim());
                if (requiredStandards.containsKey(standardId)) {
                    requiredStandards.get(standardId).add(sectionId);
                } else {
                    List<Integer> sectionList = new ArrayList<Integer>();
                    sectionList.add(sectionId);
                    requiredStandards.put(standardId, sectionList);
                }
            } else {
                UUID standardId = UUID.fromString(requiredSectionsArray[0].trim());
                requiredStandards.put(standardId, new ArrayList<Integer>());
            }
        }
        return requiredStandards;
    }

    public static Map<String, UUID> getStandardIdMap(List<Standard> standardList) {
        if(CollectionUtils.isNullOrEmpty(standardList)) {
            return null;
        }

        EMapUtils.MapFunction mapFunction = new EMapUtils.MapFunction<Standard, String, UUID>() {
            @Override
            public String getKey(Standard entry) {
                return entry.getStandardName().toLowerCase().trim() + (entry.getStream() == null || entry.getStream() == Stream.NA ? "" : "-" + entry.getStream().name().toLowerCase());
            }

            @Override
            public UUID getValue(Standard entry) {
                return entry.getStandardId();
            }
        };

        return EMapUtils.getMap(standardList, mapFunction);
    }

    public static Map<String, Standard> getStandardMap(List<Standard> standardList) {
        if(CollectionUtils.isNullOrEmpty(standardList)) {
            return null;
        }

        return EMapUtils.getMap(standardList, new EMapUtils.MapFunction<Standard, String, Standard>() {
            @Override
            public String getKey(Standard entry) {
                return entry.getStandardName().toLowerCase().trim() + (entry.getStream() == null || entry.getStream() == Stream.NA ? "" : "-" + entry.getStream().name().toLowerCase());
            }

            @Override
            public Standard getValue(Standard entry) {
                return entry;
            }
        });
    }

    public static List<UUID> getStandardIdsByStandardName(List<String> standardNameList, List<Standard> standardList, StringBuilder incorrectStandardNames) {
        EMapUtils.MapFunction<Standard, String, UUID> fun = new EMapUtils.MapFunction<Standard, String, UUID>() {
            @Override
            public String getKey(Standard entry) {
                return entry.getStandardName().toLowerCase().trim() +
                        (entry.getStream() == null || entry.getStream() == Stream.NA ? "" : "-" + entry.getStream().name().toLowerCase());
            }

            @Override
            public UUID getValue(Standard entry) {
                return entry.getStandardId();
            }
        };

        Map<String, UUID> standardMap = EMapUtils.getMap(standardList, fun);
        List<UUID> standardIdsList = new ArrayList<>();

        for (String standard : standardNameList) {
            String standardName = standard.toLowerCase().trim();
            if (standardMap.containsKey(standardName)) {
                standardIdsList.add(standardMap.get(standardName));
            } else {
                if (incorrectStandardNames.length() > 0) {
                    incorrectStandardNames.append(", ");
                }
                incorrectStandardNames.append(standard);
            }
        }
        return standardIdsList;
    }

    public static void validateStandardAndSection(UUID standardId, Set<Integer> sectionIds, Standard standard) {

		if (standardId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid standard."));
		}
		boolean standardFound = false;
		List<StandardSections> standardSectionList = null;
		if (standardId.equals(standard.getStandardId())) {
			standardFound = true;
			standardSectionList = standard.getStandardSectionList();
		}
		
		if (!standardFound) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Given standard does not exists."));
		}

		validateSection(sectionIds, standardSectionList);
	}

	private static void validateSection(Set<Integer> sectionIds, List<StandardSections> standardSectionList) {
		if (org.apache.commons.collections.CollectionUtils.isEmpty(standardSectionList)) {
            if (!CollectionUtils.isNullOrEmpty(sectionIds)) {
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                        "Given section(s) do not exist for standard."));
            }
            return;
        }

        if (CollectionUtils.isNullOrEmpty(sectionIds)) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No section(s) provided for Study Tracker."));
        }
        Set<Integer> availableSectionIds = new HashSet<Integer>();
        for (StandardSections standardSections : standardSectionList) {
            availableSectionIds.add(standardSections.getSectionId());
        }
        for (Integer sectionId : sectionIds) {
            if (!availableSectionIds.contains(sectionId)) {
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_ASSESSMENT_DATA,
                        "Selected Section does not exist for the given standard."));
            }
        }
	}
    public static String getClassName(Set<Integer> sectionIdSet, Standard standard){
        if(standard == null){
            return "";
        }
        String className = standard.getDisplayName();
        if(CollectionUtils.isNullOrEmpty(sectionIdSet)){
            return className;
        }
        int count = 0;
        for(StandardSections standardSections : standard.getStandardSectionList()){
            if(sectionIdSet.contains(standardSections.getSectionId())){
                if(count == 0){
                    className = className + " - " + standardSections.getSectionName();
                    count = count + 1;
                    continue;
                }
                className = className + " , " + standardSections.getSectionName();
            }
        }
        return className;
    }

    public static boolean validateStandards(Set<UUID> payloadStandardIds, List<Standard> instituteStandards, boolean throwError) {
        if (CollectionUtils.isNullOrEmpty(instituteStandards)) {
            if(!throwError) {
                return false;
            }
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STANDARD, "No standards found for institute and session"));
        }
        Set<UUID> validStandardIds = new HashSet<>();
        for (Standard standard : instituteStandards) {
            validStandardIds.add(standard.getStandardId());
        }

        // Check if all payload standard IDs are valid
        for (UUID standardId : payloadStandardIds) {
            if (!validStandardIds.contains(standardId)) {
                if(!throwError) {
                    return false;
                }
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_STANDARD, "Standard " + standardId + " does not belong to institute"));
            }
        }
        return true;
    }
}
