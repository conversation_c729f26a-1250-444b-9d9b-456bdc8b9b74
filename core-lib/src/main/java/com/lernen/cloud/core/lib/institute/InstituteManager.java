package com.lernen.cloud.core.lib.institute;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.api.institute.StandardMetadata;
import com.embrate.cloud.core.api.onboarding.institute.setup.InstitutePayload;
import com.embrate.cloud.core.api.salary.SalaryCycleDetailsPayload;
import com.embrate.cloud.core.api.timetable.ClassCoursesActivityDetailsPayload;
import com.embrate.cloud.core.api.timetable.CoursesActivityDetailsPayload;
import com.embrate.cloud.core.api.timetable.Entity;
import com.embrate.cloud.core.api.timetable.StaffStandardEntityDetailsRow;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.embrate.cloud.core.utils.EMapUtils;
import com.embrate.cloud.dao.tier.salary.SalaryDao;
import com.embrate.cloud.dao.tier.timetable.TimetableDao;
import com.lernen.cloud.core.api.attendance.AttendanceType;
import com.lernen.cloud.core.api.common.CounterType;
import com.lernen.cloud.core.api.common.FileData;
import com.lernen.cloud.core.api.common.StringHelper;
import com.lernen.cloud.core.api.configurations.MetaDataPreferences;
import com.lernen.cloud.core.api.course.ClassCourses;
import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.examination.ExamGrade;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.fees.FeeHeadConfiguration;
import com.lernen.cloud.core.api.fees.FeeHeadTag;
import com.lernen.cloud.core.api.fees.FeeHeadType;
import com.lernen.cloud.core.api.institute.*;
import com.lernen.cloud.core.api.organisation.InstituteConfigurationPayload;
import com.lernen.cloud.core.api.organisation.Organisation;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.staff.StaffLite;
import com.lernen.cloud.core.api.staff.StaffStatus;
import com.lernen.cloud.core.api.student.DownloadDocumentWrapper;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.document.DocumentManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.DocumentUtils;
import com.lernen.cloud.core.utils.Pair;
import com.lernen.cloud.core.utils.UUIDUtils;
import com.lernen.cloud.dao.tier.attendance.AttendanceDao;
import com.lernen.cloud.dao.tier.course.CourseDao;
import com.lernen.cloud.dao.tier.examination.ExaminationDao;
import com.lernen.cloud.dao.tier.fees.configuration.FeeConfigurationDao;
import com.lernen.cloud.dao.tier.institute.InstituteDao;
import com.lernen.cloud.dao.tier.institute.mappers.StandardRowDetailsRowMapper;
import com.lernen.cloud.dao.tier.staff.StaffDao;
import com.lernen.cloud.dao.tier.user.UserDao;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import java.time.Month;

import java.util.*;

public class InstituteManager {
	private static final Logger logger = LogManager.getLogger(InstituteManager.class);
	private final InstituteDao instituteDao;

	private final FeeConfigurationDao feeConfigurationDao;

	private final DocumentManager documentManager;
	private final UserPreferenceSettings userPreferenceSettings;

	private final ExaminationDao examinationDao;

	private final TransactionTemplate transactionTemplate;
	private final UserDao userDao;

	private final AssetProvider assetProvider;

	private final UserPermissionManager userPermissionManager;

	private final SalaryDao salaryDao;

	private final AttendanceDao attendanceDao;

	private final StaffDao staffDao;

	public InstituteManager(InstituteDao instituteDao, FeeConfigurationDao feeConfigurationDao, DocumentManager documentManager, UserPreferenceSettings userPreferenceSettings, ExaminationDao examinationDao, TransactionTemplate transactionTemplate, UserDao userDao, AssetProvider assetProvider, UserPermissionManager userPermissionManager, SalaryDao salaryDao, AttendanceDao attendanceDao, StaffDao staffDao) {
		this.instituteDao = instituteDao;
		this.feeConfigurationDao = feeConfigurationDao;
		this.documentManager = documentManager;
		this.userPreferenceSettings = userPreferenceSettings;
		this.examinationDao = examinationDao;
		this.transactionTemplate = transactionTemplate;
		this.userDao = userDao;
		this.assetProvider = assetProvider;
		this.userPermissionManager = userPermissionManager;
		this.salaryDao = salaryDao;
		this.attendanceDao = attendanceDao;
		this.staffDao = staffDao;
	}

	private void validateStandard(Standard standardPayload) {
		if (standardPayload == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STANDARD, "Invalid standard."));
		}
		if (standardPayload.getInstituteId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STANDARD, "Invalid institute id."));
		}

		if (standardPayload.getAcademicSessionId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STANDARD, "Invalid academic session id."));
		}

		if (StringUtils.isEmpty(standardPayload.getStandardName())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STANDARD, "Invalid standard name."));
		}

		if (standardPayload.getLevel() <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STANDARD, "Invalid level."));
		}
	}

	public UUID addOrganisation(InstituteConfigurationPayload instituteConfigurationPayload){
		return instituteDao.addOrganisation(instituteConfigurationPayload);
	}
	public Integer addInstitute(Institute institute) {
		if(institute == null || institute.getInstituteId() <= 0 || StringUtils.isBlank(institute.getInstituteName())){
			logger.error("Invalid institute data {}", institute);
			return null;
		}
		return instituteDao.addInstitute(institute, null);
	}

	public List<Institute> getAllInstitute() {
		return instituteDao.getAllInstitute();
	}

	public Institute getInstitute(int instituteId) {
		return instituteDao.getInstitute(instituteId);
	}

	public Map<Integer,Institute> getInstitutes(List<Integer> instituteIds) {
		if(CollectionUtils.isEmpty(instituteIds)) {
			return null;
		}
		Map<Integer, Institute> instituteMap = new HashMap<>();
		for(Integer instituteId : instituteIds) {
			if(instituteId <= 0) {
				continue;
			}
			instituteMap.put(instituteId, instituteDao.getInstitute(instituteId));
		}
		return instituteMap;
	}

	public Institute getInstitute(UUID instituteUniqueId) {
		return instituteDao.getInstitute(instituteUniqueId);
	}

	public Organisation getOrganization(int instituteId) {
		Organisation activeOrganisationData = instituteDao.getOrganization(instituteId);
		List<Institute> activeInstituteData = new ArrayList<>();
		if (activeOrganisationData == null || activeOrganisationData.getOrganisationId() == null) {
			return activeOrganisationData;
		}
		List<Institute> instituteData = activeOrganisationData.getInstitutes();
		for(Institute institute : instituteData){
			if(institute.getIsActive()){
				activeInstituteData.add(institute);
			}
		}
		activeOrganisationData.setInstitute(activeInstituteData);
		
		return activeOrganisationData;
	}

	public Organisation getOrganizationById(UUID organizationId) {
		return instituteDao.getOrganizationById(organizationId);
	}

	public boolean addCounters(int instituteId, List<CounterData> counterDataList) {
		return instituteDao.addCounters(instituteId, counterDataList);
	}

	public InstituteMetaData getInstituteMetaData(int instituteId) {

		final MetaDataPreferences metaDataPreferences = userPreferenceSettings.getMetaDataPreferences(instituteId);
		CounterData registrationCounterData = null;
		if (metaDataPreferences.isRegistrationCounter()) {
			registrationCounterData = instituteDao.getCounter(instituteId, CounterType.REGISTRATION_NUMBER, false);
		}
		CounterData admissionCounterData = null;
		if (metaDataPreferences.isAdmissionCounter()) {
			admissionCounterData = instituteDao.getCounter(instituteId, CounterType.ADMISSION_NUMBER, false);
		}
		final String nextRegistrationNumber = registrationCounterData == null ? null
				: registrationCounterData.getFullCounterValue();

		final String nextAdmissionNumber = admissionCounterData == null ? null
				: admissionCounterData.getFullCounterValue();

		CounterData staffCounterData = null;
		if (metaDataPreferences.isStaffCounter()) {
			staffCounterData = instituteDao.getCounter(instituteId, CounterType.STAFF_NUMBER, false);
		}

		final String nextStaffNumber = staffCounterData == null ? null
				: staffCounterData.getFullCounterValue();

		return new InstituteMetaData(metaDataPreferences, nextRegistrationNumber, nextAdmissionNumber, nextStaffNumber);
	}

	public List<Integer> getInstituteList(Set<Integer> instituteIdSet, boolean filterActiveInstitute){
		List<Integer> activeInstituteIdList = new ArrayList<>();
		if(!filterActiveInstitute){
			return null;
		}
		for(Integer instituteId : instituteIdSet){
				Institute institute = instituteDao.getInstitute(instituteId);
				if(institute.getIsActive()){
					activeInstituteIdList.add(instituteId);
				}
			}
		return activeInstituteIdList;
	}
	public List<AcademicSession> getAcademicSessionList(int instituteId) {
		List<AcademicSession> academicSessionList  = instituteDao.getAcademicSessionList(instituteId);
		Collections.sort(new ArrayList<>(academicSessionList), new Comparator<AcademicSession>() {
			@Override
			public int compare(AcademicSession s1, AcademicSession s2) {
				return s2.getSessionStartTime() - s1.getSessionStartTime();
			}
		});
		return academicSessionList;
	}

	public AcademicSession getAcademicSession(int instituteId, int academicSessionId) {
		return instituteDao.getAcademicSession(instituteId, academicSessionId);
	}

	public Map<Integer, List<AcademicSession>> getInstituteAcademicSessionMap(List<Integer> instituteIdList) {
		return instituteDao.getInstituteAcademicSessionMap(instituteIdList);
	}

	public AcademicSession getAcademicSessionByAcademicSessionId(int academicSessionId) {
		return instituteDao.getAcademicSessionByAcademicSessionId(academicSessionId);
	}

	public UUID addInstituteStandard(Standard standardPayload) {
		validateStandard(standardPayload);
		return instituteDao.addInstituteStandard(standardPayload);
	}

	public boolean updateInstituteStandard(int instituteId, UUID userId, Map<String, String> standardNamesMap) {

		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
		}
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.UPDATE_STANDARD);
		validateStandardNameMap(standardNamesMap);

		List<Pair<UUID, Pair<String, String>>> standardNamePairs = new ArrayList<>();

		List<Standard> standardList = instituteDao.getStandardDetailsWithoutSession(Collections.singletonList(instituteId));
		if (CollectionUtils.isEmpty(standardList)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STANDARD, "This institute do not have standards."));
		}

		Map< String, Standard> standardNameMap = new HashMap<>();
		for(Standard standard : standardList){
			String standardName = standard.getStandardName();
			String stream = standard.getStream()==null?"NA":standard.getStream().toString();

			String fullStandardName = standardName.toUpperCase() + "-" + stream.toUpperCase();
			standardNameMap.put(fullStandardName,standard);
		}

		for (Map.Entry<String, String> standardNames : standardNamesMap.entrySet()) {
			String oldStandardName = standardNames.getKey();
			String newStandardName = standardNames.getValue();

			String[] oldStandardSplit = oldStandardName.split("-", 2);
			String[] newStandardSplit = newStandardName.split("-", 2);

			String oldName = oldStandardSplit[0].toUpperCase();
			String oldStream = oldStandardSplit.length > 1 ? oldStandardSplit[1].toUpperCase() : "NA";

			String newName = newStandardSplit[0].toUpperCase();
			String newStream = newStandardSplit.length > 1 ? newStandardSplit[1].toUpperCase() : "NA";

			oldStandardName = oldName + "-" + oldStream;
			if(standardNameMap.containsKey(oldStandardName)){
				standardNamePairs.add(new Pair<>(
						standardNameMap.get(oldStandardName).getStandardId(),
						new Pair<>(newName, newStream)
				));
			}
		}

		if (standardNamePairs.size() != standardNamesMap.size()) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STANDARD, "Ensure you provide a valid old standard name."));
		}

		return instituteDao.updateInstituteStandard(instituteId, standardNamePairs);
	}

	private void validateStandardNameMap(Map<String, String> standardNamesMap) {
		if (standardNamesMap.isEmpty()) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STANDARD, "Ensure you provide a valid standards list."));
		}

		for (Map.Entry<String, String> standardName : standardNamesMap.entrySet()) {
			String oldStandardName = standardName.getKey();
			String newStandardName = standardName.getValue();

			if (StringUtils.isEmpty(oldStandardName)) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_STANDARD, "Ensure you provide an old standard name."));
			}
			if (StringUtils.isEmpty(newStandardName)) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_STANDARD, "Ensure you provide a new standard name."));
			}
			if (oldStandardName.equals(newStandardName)) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_STANDARD, "Ensure the new standard name is different from the old one for " + oldStandardName));
			}
		}
	}

	public boolean addUpdateCounterValue(int instituteId, UUID userId, List<CounterData> counterDataList) {
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
		}
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.UPDATE_COUNTER_VALUE);
		validateCounterData(counterDataList);

		return addCounters(instituteId, counterDataList);

	}

	private void validateCounterData(List<CounterData> counterDataList) {

		if (CollectionUtils.isEmpty(counterDataList)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COUNTERS_DETAILS, "Counter data cannot be empty."));

		}

		for (CounterData counterData : counterDataList) {
			if (counterData.getCounterType() == null) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_COUNTERS_DETAILS, "Counter type cannot be null."));
			}
			if (counterData.getCount() <= 0) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_COUNTERS_DETAILS, "Count must be greater then 0."));
			}
		}
	}

	public List<Standard> getInstituteStandardList(int instituteId) {
		return sortStandardSectionsList(instituteDao.getInstituteStandardList(instituteId, null));
	}

	public Standard getStandardByName(int instituteId, String standardName) {
		List<Standard> standardList = getInstituteStandardList(instituteId);
		for (Standard standard : standardList) {
			if (standard.getDisplayName().equalsIgnoreCase(standardName)) {
				return standard;
			}
		}
		return null;
	}


	public List<Standard> getInstituteStandardList(int instituteId, Integer academicSessionId) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		if (academicSessionId <= 0 || academicSessionId == null ) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id."));
		}

		return sortStandardSectionsList(instituteDao.getInstituteStandardList(instituteId, academicSessionId));
	}

	public List<Standard> sortStandardSectionsList(List<Standard> standardList){

		if(CollectionUtils.isEmpty(standardList)){
			return null;
		}

		for (final Standard standard : standardList) {
			List<StandardSections> standardSectionsList = standard.getStandardSectionList();
			if(!CollectionUtils.isEmpty(standardSectionsList)) {
				Collections.sort(standardSectionsList, new Comparator<StandardSections>() {
					@Override
					public int compare(StandardSections o1, StandardSections o2) {
						return o1.getSectionName().compareTo(o2.getSectionName());
					}
				});
			}
		}
		return standardList;
	}

	public List<Standard> getInstituteStandardDetails(int instituteId, int academicSessionId) {
		return sortStandardSectionsList(instituteDao.getInstituteStandardDetails(instituteId, academicSessionId));
	}

	public Map<Integer, Map<Integer, List<Standard>>> getAllSessionStandardDetailsWithoutStudentCount(List<Integer> instituteIdList) {
		Map<Integer, List<AcademicSession>> instituteAcademicSessionMap = getInstituteAcademicSessionMap(instituteIdList);
		return getAllSessionStandardDetailsWithoutStudentCount(instituteIdList, instituteAcademicSessionMap);
	}

	public Map<Integer, Map<Integer, List<Standard>>> getAllSessionStandardDetailsWithoutStudentCount(List<Integer> instituteIdList,
					Map<Integer, List<AcademicSession>> instituteAcademicSessionMap) {
			List<Standard> standardList = instituteDao.getStandardDetailsWithoutSession(instituteIdList);
			//InstituteId, List<Standard>
			Map<Integer, List<Standard>> standardMap = new HashMap<>();
			for(Standard standard : standardList) {
				if(!standardMap.containsKey(standard.getInstituteId())) {
					standardMap.put(standard.getInstituteId(), new ArrayList<>());
				}
				standardMap.get(standard.getInstituteId()).add(standard);
			}
			return getAllSessionStandardDetails(instituteDao.getAllSessionStandardDetailsWithoutStudentCount(instituteIdList),
					instituteAcademicSessionMap, standardMap);
	}

	//InstituteId, AcademicSessionId, List<Standards>
	public Map<Integer, Map<Integer, List<Standard>>> getAllSessionStandardDetails(List<StandardRowDetails> standardRowDetailsList,
					Map<Integer, List<AcademicSession>> instituteAcademicSessionMap, Map<Integer, List<Standard>> standardMap) {
		final Map<Integer, Map<Integer, Map<UUID, Standard>>> instituteSessionStandardMap = new HashMap<>();
		if (org.springframework.util.CollectionUtils.isEmpty(standardRowDetailsList)) {
			return new HashMap<>();
		}

//		InstituteId, AcademicSessionId, StandardId, List<StandardRowDetails>
		final Map<Integer, Map<Integer, Map<UUID, List<StandardRowDetails>>>> instituteSessionStandardRowDetailsMap = new HashMap<>();

		for(Map.Entry<Integer, List<AcademicSession>> instituteAcademicSessionEntry : instituteAcademicSessionMap.entrySet()) {
			int instituteId = instituteAcademicSessionEntry.getKey();
			if(!instituteSessionStandardRowDetailsMap.containsKey(instituteId)) {
				if(instituteId <= 0) {
					continue;
				}
				instituteSessionStandardRowDetailsMap.put(instituteId, new HashMap<>());
			}
			List<AcademicSession> academicSessionList = instituteAcademicSessionEntry.getValue();
			for(AcademicSession academicSession : academicSessionList) {
				int academicSessionId = academicSession.getAcademicSessionId();
				if(academicSessionId <= 0) {
					continue;
				}
				if(!instituteSessionStandardRowDetailsMap.get(instituteId).containsKey(academicSessionId)) {
					instituteSessionStandardRowDetailsMap.get(instituteId).put(academicSessionId, new HashMap<>());
				}
			}
		}

		for (final StandardRowDetails standardRowDetails : standardRowDetailsList) {
			int instituteId = standardRowDetails.getInstituteId();
			Map<Integer, Map<UUID, List<StandardRowDetails>>> sessionStandardRowDetailsMap = instituteSessionStandardRowDetailsMap.get(instituteId);
			if(sessionStandardRowDetailsMap == null || CollectionUtils.isEmpty(sessionStandardRowDetailsMap.entrySet())) {
				continue;
			}
			int academicSessionId = standardRowDetails.getAcademicSessionId();
			UUID standardId = standardRowDetails.getStandardId();
			if(academicSessionId <= 0) {
				for(Map.Entry<Integer, Map<UUID, List<StandardRowDetails>>> sessionStandardRowDetailsEntry : sessionStandardRowDetailsMap.entrySet()) {
					Map<UUID, List<StandardRowDetails>> standardRowDetailsMap = sessionStandardRowDetailsEntry.getValue();
					if(!standardRowDetailsMap.containsKey(standardId)) {
						standardRowDetailsMap.put(standardId, new ArrayList<>());
					}
					standardRowDetailsMap.get(standardId).add(standardRowDetails);
				}
				continue;
			}
			Map<UUID, List<StandardRowDetails>> standardRowDetailsMap = sessionStandardRowDetailsMap.get(academicSessionId);
//			if(standardRowDetailsMap == null || CollectionUtils.isEmpty(standardRowDetailsMap.entrySet())) {
//				standardRowDetailsMap = new HashMap<>();
//			}
			if(!standardRowDetailsMap.containsKey(standardId)) {
				standardRowDetailsMap.put(standardId, new ArrayList<>());
			}
			/**
			 * this case is specific to standards which do not have section in one session and have in another.
			 */
			if(standardRowDetailsMap.get(standardId).size() == 1 && standardRowDetailsMap.get(standardId).get(0).getSectionId() == null){
				standardRowDetailsMap.put(standardId, new ArrayList<>());
			}
			standardRowDetailsMap.get(standardId).add(standardRowDetails);
		}

		for(Map.Entry<Integer, Map<Integer, Map<UUID, List<StandardRowDetails>>>> instituteSessionStandardRowDetailsEntry : instituteSessionStandardRowDetailsMap.entrySet()) {
			int instituteId = instituteSessionStandardRowDetailsEntry.getKey();
			if(!instituteSessionStandardMap.containsKey(instituteId)) {
				instituteSessionStandardMap.put(instituteId, new HashMap<>());
			}
			Map<Integer, Map<UUID, List<StandardRowDetails>>> sessionStandardRowDetailsMap = instituteSessionStandardRowDetailsEntry.getValue();
			for(Map.Entry<Integer, Map<UUID, List<StandardRowDetails>>> sessionStandardRowDetailsEntry : sessionStandardRowDetailsMap.entrySet()) {
				int academicSessionId = sessionStandardRowDetailsEntry.getKey();
				if(!instituteSessionStandardMap.get(instituteId).containsKey(academicSessionId)) {
					instituteSessionStandardMap.get(instituteId).put(academicSessionId, new HashMap<>());
				}
				Map<UUID, List<StandardRowDetails>> standardRowDetailsMap = sessionStandardRowDetailsEntry.getValue();
				for(Map.Entry<UUID, List<StandardRowDetails>> standardRowDetailsEntry : standardRowDetailsMap.entrySet()) {
					List<StandardRowDetails> standardRowDetailsList1 = standardRowDetailsEntry.getValue();
					final Standard standard = StandardRowDetailsRowMapper.getStandardResponse(standardRowDetailsList1);
					if(standard == null) {
						continue;
					}
					standard.setAcademicSessionId(academicSessionId);
					instituteSessionStandardMap.get(instituteId).get(academicSessionId).put(standard.getStandardId(), standard);
				}
			}
		}


		for(Map.Entry<Integer, Map<Integer, Map<UUID, Standard>>> instituteSessionStandardEntry : instituteSessionStandardMap.entrySet()) {
			int instituteId = instituteSessionStandardEntry.getKey();
			Map<Integer, Map<UUID, Standard>> sessionStandardMap = instituteSessionStandardEntry.getValue();
				List<Standard> standardList = standardMap.get(instituteId);
//			if(CollectionUtils.isEmpty(standardList)) {
//				continue;
//			}
			for(Map.Entry<Integer, Map<UUID, Standard>> sessionStandardEntry : sessionStandardMap.entrySet()) {
				int academicSessionId = sessionStandardEntry.getKey();
				for(Standard standard : standardList) {
					if(standard == null) {
						continue;
					}
					standard.setAcademicSessionId(academicSessionId);
					if(!sessionStandardEntry.getValue().containsKey(standard.getStandardId())) {
						sessionStandardEntry.getValue().put(standard.getStandardId(), standard);
					}
				}
			}
		}

		final Map<Integer, Map<Integer, List<Standard>>> finalInstituteSessionStandardMap = new HashMap<>();
		for(Map.Entry<Integer, Map<Integer, Map<UUID, Standard>>> instituteSessionStandardEntry : instituteSessionStandardMap.entrySet()) {
			int instituteId = instituteSessionStandardEntry.getKey();
			Map<Integer, Map<UUID, Standard>> sessionStandardMap = instituteSessionStandardEntry.getValue();
			if(!finalInstituteSessionStandardMap.containsKey(instituteId)) {
				finalInstituteSessionStandardMap.put(instituteId, new HashMap<>());
			}
			for(Map.Entry<Integer, Map<UUID, Standard>> sessionStandardEntry : sessionStandardMap.entrySet()) {
				int academicSessionId = sessionStandardEntry.getKey();
				if(!finalInstituteSessionStandardMap.get(instituteId).containsKey(academicSessionId)) {
					finalInstituteSessionStandardMap.get(instituteId).put(academicSessionId, new ArrayList<>(sessionStandardEntry.getValue().values()));
					sortStandardsList(finalInstituteSessionStandardMap.get(instituteId).get(academicSessionId));
				}
			}
		}
		return finalInstituteSessionStandardMap;
	}

	public void sortStandardsList(List<Standard> standardList) {
		if(org.springframework.util.CollectionUtils.isEmpty(standardList)) {
			return;
		}

		for(Standard standard : standardList) {
			List<StandardSections> standardSectionsList = standard.getStandardSectionList();
			if(!CollectionUtils.isEmpty(standardSectionsList)) {
				Collections.sort(standardSectionsList, new Comparator<StandardSections>() {
					@Override
					public int compare(StandardSections o1, StandardSections o2) {
						return o1.getSectionName().compareTo(o2.getSectionName());
					}
				});
			}
		}

		Collections.sort(standardList, new Comparator<Standard>() {
			@Override
			public int compare(Standard s1, Standard s2) {
				return s1.getLevel() - s2.getLevel();
			}
		});
	}
	public static boolean isTransportModuleFeeHead(FeeHeadConfiguration feeHeadConfiguration) {
		return feeHeadConfiguration != null && feeHeadConfiguration.getFeeHeadType() == FeeHeadType.SYSTEM
				&& feeHeadConfiguration.getFeeHeadTag() == FeeHeadTag.TRANSPORT;
	}

	public Integer getTransportFeeHead(int instituteId) {
		return feeConfigurationDao.getTransportFeeHead(instituteId);
	}

	public List<String> addInstituteSession(AcademicSession inputAcademicSession, int cloneAcademicSessionId, boolean skipCloning) {
		List<String> errorMessages = new ArrayList<>();
		validateInputAcademicSessionDetails(inputAcademicSession, errorMessages);
		if(CollectionUtils.isEmpty(errorMessages)){
			addInstituteSession(inputAcademicSession, cloneAcademicSessionId, skipCloning, errorMessages);
		}
		return errorMessages;
	}

	private void validateInputAcademicSessionDetails(AcademicSession inputAcademicSession, List<String> errorMessages){
		if(inputAcademicSession.getInstituteId() <= 0){
			errorMessages.add("Invalid Institute ID please Verify");
		}
		if(inputAcademicSession.getStartYear() > inputAcademicSession.getEndYear()){
			errorMessages.add("The start year (" + inputAcademicSession.getStartYear() + ") cannot be greater than the end year (" + inputAcademicSession.getEndYear() + ").");
			return;
		}
		if(inputAcademicSession.getPayrollStartYear() > inputAcademicSession.getPayrollEndYear()){
			errorMessages.add("The payroll start year (" + inputAcademicSession.getStartYear() + ") cannot be greater than the payroll end year (" + inputAcademicSession.getEndYear() + ").");
			return;
		}
		List<AcademicSession> existingAcademicSessionList = getAcademicSessionList(inputAcademicSession.getInstituteId());
		if(CollectionUtils.isEmpty(existingAcademicSessionList)){
			return;
		}
		for (AcademicSession existingAcademicSession : existingAcademicSessionList){
			checkDuplicateYearMonth(
				existingAcademicSession.getStartYear(), existingAcademicSession.getStartMonth(),
				inputAcademicSession.getStartYear(), inputAcademicSession.getStartMonth(),
				"academic", errorMessages
        	);

			checkDuplicateYearMonth(
				existingAcademicSession.getPayrollStartYear(), existingAcademicSession.getPayrollStartMonth(),
				inputAcademicSession.getPayrollStartYear(), inputAcademicSession.getPayrollStartMonth(),
				"payroll", errorMessages
			);
		}

	}

	private void checkDuplicateYearMonth(int existingYear, Month existingMonth, int inputYear, Month inputMonth, String type, List<String> errorMessages) {
		if (existingYear == inputYear && existingMonth == inputMonth) {
			errorMessages.add("An " + type + " session with the same start year (" +
					inputYear + ") and start month (" + inputMonth + ") already exists.");
		}
	}


	public boolean addInstituteSession(AcademicSession inputAcademicSession, int cloneAcademicSessionId, boolean skipCloning, List<String> errorMessages) {
		if (skipCloning) {
			return createSessionWithoutCloning(inputAcademicSession, errorMessages);
		}

		if(cloneAcademicSessionId <= 0 || inputAcademicSession == null || inputAcademicSession.getInstituteId() <= 0){
			errorMessages.add("Invalid payload cloneAcademicSessionId , inputAcademicSession ");
			logger.error("Invalid payload cloneAcademicSessionId {}, inputAcademicSession {}", cloneAcademicSessionId, inputAcademicSession);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request"));
		}

		try {
			return transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus transactionStatus) {
					boolean sessionCreated = instituteDao.addInstituteSession(inputAcademicSession);
					if (!sessionCreated) {
						errorMessages.add("Unable to create session");
						logger.error("Unable to create session {}", inputAcademicSession);
						throw new EmbrateRunTimeException("Unable to create session");
					}
					int instituteId = inputAcademicSession.getInstituteId();
					Pair<AcademicSession, AcademicSession> output = getPrevAndLatestSession(instituteId, cloneAcademicSessionId, inputAcademicSession, errorMessages);
					AcademicSession prevAcademicSession = output.getFirst();
					AcademicSession latestAcademicSession = output.getSecond();
					int prevSessionId = prevAcademicSession.getAcademicSessionId();
					int latestSessionId = latestAcademicSession.getAcademicSessionId();

					List<Standard> standardList = getInstituteStandardList(instituteId, prevSessionId);

					List<StandardMetadata> newSessionStandardMetadataList = new ArrayList<>();
					List<ExamGrade> examGradeList = new ArrayList<>();

					for (Standard standard : standardList) {
						if (CollectionUtils.isNotEmpty(standard.getStandardSectionList())) {
							boolean success = instituteDao.addStandardSectionMapping(standard.getStandardId(), latestSessionId, standard.getStandardSectionList());
							if (!success) {
								errorMessages.add("Error in copying sections for standard , latestSessionId ");
								logger.error("Error in copying sections for standard {}, latestSessionId {}", standard.getStandardId(), latestSessionId);
								throw new EmbrateRunTimeException("Error in section copy");
							}
						}

						StandardMetadata standardMetaData = getStandardMetaData(instituteId, prevSessionId, standard.getStandardId());
						newSessionStandardMetadataList.add(new StandardMetadata(instituteId, standard.getStandardId(), latestSessionId, standardMetaData.isCoScholasticGradingEnabled(), standardMetaData.isScholasticGradingEnabled(), standardMetaData.isRoundExamReportMarks()));

						Map<CourseType, List<ExamGrade>> prevSessionGradesMap = examinationDao.getExamGrades(instituteId, prevSessionId, standard.getStandardId());
						for (Map.Entry<CourseType, List<ExamGrade>> entry : prevSessionGradesMap.entrySet()) {
							for (ExamGrade examGrade : entry.getValue()) {
								examGradeList.add(new ExamGrade(instituteId, latestSessionId, examGrade.getStandardId(),
										examGrade.getCourseType(), null, examGrade.getGradeName(), examGrade.getMarksRangeStart(),
										examGrade.getMarksRangeEnd(), examGrade.getGradeValue(), examGrade.getRangeDisplayName(), examGrade.getRemarks(), examGrade.getCreditScore()));
							}
						}

					}
					boolean metadataSuccess = addStandardMetadata(instituteId, latestSessionId, newSessionStandardMetadataList);
					if (!metadataSuccess) {
						logger.error("Error in copying standard metadata for institute {}, latestSessionId {}", instituteId, latestSessionId);
						throw new EmbrateRunTimeException("Error in copying standard metadata");
					}

					boolean examGradeSuccess = examinationDao.addExamGrades(instituteId, latestSessionId, examGradeList);
					if (!examGradeSuccess) {
						logger.error("Error in copying exam grades for institute {}, latestSessionId {}", instituteId, latestSessionId);
						throw new EmbrateRunTimeException("Error in copying exam grades");
					}

					boolean salaryCycle = addSalaryCycleNewSessionSetup(instituteId, latestAcademicSession);
					if (!salaryCycle) {
						logger.error("No salary cycle created for latestCreatedSession {}", latestAcademicSession);
					}

					boolean cloneStandardWithStaffDetails = cloneStandardWithStaffDetails(instituteId, instituteId, prevSessionId, latestSessionId, errorMessages, false);
					if(!cloneStandardWithStaffDetails){
						logger.error("No Class Teacher Assigned for latestCreatedSession {}", latestAcademicSession);
						throw new EmbrateRunTimeException("Error in copying exam grades");
					}
					transportAttendanceTypeNewSessionSetup(instituteId, latestSessionId);
					return true;
				}
			});
		} catch (Exception e) {
			logger.error("Unable to create session {}", inputAcademicSession, e);
		}
		return false;
	}

	private boolean createSessionWithoutCloning(AcademicSession inputAcademicSession, List<String> errorMessages) {
		try {
			return transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus transactionStatus) {
					boolean sessionCreated = instituteDao.addInstituteSession(inputAcademicSession);
					if (!sessionCreated) {
						errorMessages.add("Unable to create session");
						logger.error("Unable to create session {}", inputAcademicSession);
						throw new EmbrateRunTimeException("Unable to create session");
					}
					int instituteId = inputAcademicSession.getInstituteId();

					AcademicSession latestSession = instituteDao.getLatestSessionDetails(instituteId);

					boolean salaryCycleCreated = addSalaryCycleNewSessionSetup(instituteId, latestSession);

					if (!salaryCycleCreated ) {
						errorMessages.add("Failed to create salary cycle for latestSession");
						logger.error("Failed to create salary cycle for latestSession {}", latestSession);
					}
					boolean transportAttendanceTypeCreated = transportAttendanceTypeNewSessionSetup(instituteId, latestSession.getAcademicSessionId());

					if (!transportAttendanceTypeCreated) {
						errorMessages.add("Failed to create transport attendance type for latestSession ");
						logger.error("Failed to create transport attendance type for latestSession {}", latestSession);
					}
					return true;
				}
			});
		} catch (Exception e) {
			logger.error("Unable to create session {}", inputAcademicSession, e);
		}
		return false;
	}

	private boolean transportAttendanceTypeNewSessionSetup(int instituteId, int academicSessionId) {
		AttendanceType pickupAttendanceType = new AttendanceType(instituteId, academicSessionId, 0, "Pickup", null, null, null);
		AttendanceType dropAttendanceType = new AttendanceType(instituteId, academicSessionId, 0, "Drop", null, null, null);
		List<AttendanceType> attendanceTypeList = new ArrayList<>(Arrays.asList(
				pickupAttendanceType, dropAttendanceType
		));
		for (AttendanceType attendanceType : attendanceTypeList) {
			boolean transportAttendanceTypeCreated = attendanceDao.addAttendanceType(attendanceType, Module.TRANSPORT);
			if (!transportAttendanceTypeCreated) {
				logger.error("Error in creating transport attendance type for institute {}, academicSessionId {}", instituteId, academicSessionId);
				throw new EmbrateRunTimeException("Error in creating transport attendance type");
			}
		}

		return true;
	}



	private Pair<AcademicSession, AcademicSession> getPrevAndLatestSession(int instituteId, int cloneAcademicSessionId, AcademicSession inputAcademicSession, List<String> errorMessages) {
		List<AcademicSession> academicSessionList = instituteDao.getAcademicSessionList(instituteId);
		AcademicSession prevCloneSession = null;
		AcademicSession latestCreatedSession = null;
		for (AcademicSession academicSession : academicSessionList) {
			if (academicSession.getAcademicSessionId() == cloneAcademicSessionId) {
				//req prev session
				prevCloneSession = academicSession;
			}
			if (academicSession.getStartMonth() == inputAcademicSession.getStartMonth()
					&& academicSession.getEndMonth() == inputAcademicSession.getEndMonth()
					&& academicSession.getStartYear() == inputAcademicSession.getStartYear()
					&& academicSession.getEndYear() == inputAcademicSession.getEndYear()) {
				//newly created session
				latestCreatedSession = academicSession;
			}
		}
		if (prevCloneSession == null || latestCreatedSession == null) {
			errorMessages.add("Invalid prevCloneSession or latestCreatedSession ");
			logger.error("Invalid prevCloneSession {} or latestCreatedSession {}", cloneAcademicSessionId, latestCreatedSession);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid prev clone session or current session not found"));
		}

		return new Pair<>(prevCloneSession, latestCreatedSession);
	}

	private boolean addSalaryCycleNewSessionSetup(int instituteId, AcademicSession latestCreatedSession) {

		List<SalaryCycleDetailsPayload> salaryCycleDetailsPayloadList = new ArrayList<>();
		int currentYear = latestCreatedSession.getStartYear();
		int currentMonth = latestCreatedSession.getStartMonth().getValue();

		int endYear = latestCreatedSession.getEndYear();
		int endMonth = latestCreatedSession.getEndMonth().getValue();

		while (currentYear < endYear || (currentYear == endYear && currentMonth <= endMonth)) {
			Month month = DateUtils.getMonth(currentMonth);
			String cycleName = month.name() + " " + currentYear;

			int startDateTimeStamp = DateUtils.getFirstDayOfMonth(month, currentYear, DateUtils.DEFAULT_TIMEZONE);
			int endDateTimeStamp = DateUtils.getLastDayOfMonth(month, currentYear, DateUtils.DEFAULT_TIMEZONE);

			salaryCycleDetailsPayloadList.add(new SalaryCycleDetailsPayload(startDateTimeStamp, endDateTimeStamp, cycleName));

			currentMonth++;
			if (currentMonth > 12) {
				currentMonth = 1;
				currentYear++;
			}
		}
		return salaryDao.addBulkSalaryCycles(instituteId, latestCreatedSession.getAcademicSessionId(), salaryCycleDetailsPayloadList);
	}

	public boolean validStandard(UUID standardId, int instituteId, int academicSessionId) {
		final List<Standard> standards = getInstituteStandardList(instituteId, academicSessionId);
		for (final Standard standard : standards) {
			if (standard.getStandardId().equals(standardId)) {
				return true;
			}
		}
		return false;
	}

	public StandardMetadata getStandardMetaData(int instituteId, int academicSessionId, UUID standardId) {
		final StandardMetadata standardMetaData = instituteDao.getStandardMetaData(instituteId, academicSessionId,
				standardId);
		if (standardMetaData == null) {
			return new StandardMetadata(instituteId, standardId, academicSessionId, false, false, true);
		}
		return standardMetaData;
	}

	public boolean addStandardMetadata(int instituteId, int academicSessionId, List<StandardMetadata> standardMetadataList) {
		return instituteDao.addStandardMetadata(instituteId, academicSessionId, standardMetadataList);
	}

	public CounterData getCounter(int instituteId, CounterType counterType, boolean forUpdate) {
		if ((instituteId <= 0) || (counterType == null)) {
			return null;
		}
		return instituteDao.getCounter(instituteId, counterType, forUpdate);
	}

	public List<CounterData> getCounters(int instituteId, String counterTypes) {
		if ((instituteId <= 0) || StringUtils.isBlank(counterTypes)) {
			return null;
		}
		String [] counterTypesToken = counterTypes.split(",");
		Set<CounterType> counterTypeSet = new HashSet<>();
		for(String counterType : counterTypesToken){
			counterTypeSet.add(CounterType.valueOf(counterType));
		}
		return instituteDao.getCounters(instituteId, counterTypeSet);
	}

	public Boolean updateCounter(int instituteId, CounterType counterType, int count) {
		if ((instituteId <= 0) || (counterType == null)) {
			return null;
		}
		return instituteDao.updateCounter(instituteId, counterType, count);
	}

	public boolean updateCounterPrefix(int instituteId, CounterType counterType, String counterPrefix) {
		if (instituteId <= 0 || counterType == null || counterPrefix == null) {
			return false;
		}
		return instituteDao.updateCounterPrefix(instituteId, counterType, counterPrefix);
	}

	public AcademicSession getNextSessionDetails(int instituteId, int currentAcademicSessionId) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		if (currentAcademicSessionId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id."));
		}
		return instituteDao.getNextSessionDetails(instituteId, currentAcademicSessionId);
	}

	public AcademicSession getPreviousSessionDetails(int instituteId, int currentAcademicSessionId) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		if (currentAcademicSessionId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id."));
		}
		return instituteDao.getPreviousSessionDetails(instituteId, currentAcademicSessionId);
	}

	public AcademicSession getLatestSessionDetails(int instituteId) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		return instituteDao.getLatestSessionDetails(instituteId);
	}

	public AcademicSession getCurrentDateSessionDetails(int instituteId) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		return instituteDao.getCurrentDateSessionDetails(instituteId);
	}

	public UUID addInstituteHouse(InstituteHouse instituteHouse, UUID userId) {
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		validateInstituteHousePayload(instituteHouse, false);

		InstituteHouse existingInstituteHouse = instituteDao.checkInstituteHouseByName(instituteHouse.getInstituteId(),
				instituteHouse.getHouseName());
		if(existingInstituteHouse != null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Institute House already exists with given name. "
							+ "Please change the name and try again."));
		}

		return instituteDao.addInstituteHouse(instituteHouse, userId);
	}

	private void validateInstituteHousePayload(InstituteHouse instituteHouse, boolean update) {
		if (instituteHouse.getInstituteId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if(update) {
			if (instituteHouse.getHouseId() == null) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
			}
		}

		if (StringUtils.isBlank(instituteHouse.getHouseName())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
	}

	public InstituteHouse getInstituteHousesDetailsByHouseId(int instituteId, UUID houseId) {

		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (houseId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid house id."));
		}


		return instituteDao.getInstituteHousesDetailsByHouseId(instituteId, houseId);
	}

	public List<InstituteHouse> getInstituteHouseList(int instituteId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		return instituteDao.getInstituteHouseList(instituteId);
	}

	public boolean updateInstituteHouse(InstituteHouse instituteHouse, UUID userId) {
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		validateInstituteHousePayload(instituteHouse, true);

		InstituteHouse existingInstituteHouse = instituteDao.checkInstituteHouseByName(instituteHouse.getInstituteId(),
				instituteHouse.getHouseName());
		if(existingInstituteHouse != null) {
			if(!existingInstituteHouse.getHouseId().equals(instituteHouse.getHouseId())) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Institute House already exists with given name. "
								+ "Please change the name and try again."));
			}
		}
		return instituteDao.updateInstituteHouse(instituteHouse, userId);
	}

	public boolean deleteInstituteHouse(int instituteId, UUID userId, UUID houseId) {

		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (houseId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		List<InstituteHousesWithCount> instituteHousesWithCountList = instituteDao.getInstituteHousesWithCount(instituteId);
		for(InstituteHousesWithCount instituteHousesWithCount : instituteHousesWithCountList) {
			if(instituteHousesWithCount.getInstituteHouse().getHouseId().equals(houseId) &&
				instituteHousesWithCount.getStudentCount() > 0) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "House is assign to student(s). "
								+ "Please change their house and try again."));
			}
		}
		return instituteDao.deleteInstituteHouse(instituteId, houseId);
	}

	public List<InstituteHousesWithCount> getInstituteHousesWithCount(int instituteId, int academicSessionId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		List<InstituteHousesWithCount> instituteHousesWithCountFinal = new ArrayList<>();
		List<InstituteHouse> instituteHouseList = getInstituteHouseList(instituteId);
		Map<UUID, InstituteHousesWithCount> instituteHouseWithCountMap = InstituteHousesWithCount.getInstituteHouseWithCountMap(
				instituteDao.getInstituteHousesWithCount(instituteId, academicSessionId));
		for(InstituteHouse instituteHouse : instituteHouseList) {
			int count = 0;
			if(instituteHouseWithCountMap != null && !CollectionUtils.isEmpty(instituteHouseWithCountMap.entrySet())
					&& instituteHouseWithCountMap.get(instituteHouse.getHouseId()) != null) {
				count = instituteHouseWithCountMap.get(instituteHouse.getHouseId()).getStudentCount();
			}
			instituteHousesWithCountFinal.add(new InstituteHousesWithCount(instituteHouse, count));
		}
		return instituteHousesWithCountFinal;
	}

	public Map<UUID, Standard> getInstituteStandardDetailsMap(int instituteId, int academicSessionId) {
		Map<UUID, Standard> standardMap = new HashMap<>();
		List<Standard> standardList = sortStandardSectionsList(instituteDao.getInstituteStandardDetails(instituteId, academicSessionId));
		if(CollectionUtils.isEmpty(standardList)) {
			return null;
		}
		for(Standard standard : standardList) {
			if(!standardMap.containsKey(standard.getStandardId())) {
				standardMap.put(standard.getStandardId(), standard);
			}
		}
		return standardMap;
	}

	public List<Institute> getInstitutesByOrganisationId(UUID organisationId) {
		return instituteDao.getInstitutesByOrganisationId(organisationId);
	}

	public Map<Integer, Integer> getAllInstituteSessionDetailsBySelectedSession(UUID organisationId, UUID userId,
																				int selectedAcademicSessionId) {

		Organisation organisation = getOrganizationById(organisationId);
		if (organisation == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid organisation"));
		}

		AcademicSession selectedAcademicSession = getAcademicSessionByAcademicSessionId(selectedAcademicSessionId);
		User user = userDao.getUser(userId);
		return getAllInstituteSessionDetailsBySelectedSession(organisation, user, selectedAcademicSession);
	}

	public Map<Integer, Integer> getAllInstituteSessionDetailsBySelectedSession(Organisation organisation, User user,
																				AcademicSession selectedAcademicSession) {

		if(organisation == null || org.springframework.util.CollectionUtils.isEmpty(organisation.getInstitutes())
				|| user == null || org.springframework.util.CollectionUtils.isEmpty(user.getInstituteScope())) {
			return new HashMap<>();
		}

		Integer selectedAcademicSessionStartTime = selectedAcademicSession.getSessionStartTime();

		List<Integer> instituteScopeList = user.getInstituteScope();

		Map<Integer, List<AcademicSession>> instituteAcademicSessionMap = getInstituteAcademicSessionMap(instituteScopeList);

		if(instituteAcademicSessionMap == null || CollectionUtils.isEmpty(instituteAcademicSessionMap.entrySet())) {
			return new HashMap<>();
		}

		Map<Integer, Integer> instituteIdSessionIdMap = new HashMap<>();
		for(Map.Entry<Integer, List<AcademicSession>> instituteAcademicSessionEntry : instituteAcademicSessionMap.entrySet()) {
			if(instituteAcademicSessionEntry == null) {
				continue;
			}
			int instituteId = instituteAcademicSessionEntry.getKey();
			if(instituteId <= 0) {
				continue;
			}
			if(!instituteIdSessionIdMap.containsKey(instituteId)) {
				instituteIdSessionIdMap.put(instituteId, null);
			}
			List<AcademicSession> academicSessionList = instituteAcademicSessionEntry.getValue();
			if(CollectionUtils.isEmpty(academicSessionList)) {
				continue;
			}

			for(AcademicSession academicSession : academicSessionList) {
				Integer sessionStartTime = academicSession.getSessionStartTime();
				Integer sessionEndTime = academicSession.getSessionEndTime();
				boolean currentSession = selectedAcademicSessionStartTime >= sessionStartTime && selectedAcademicSessionStartTime <= sessionEndTime;
				if(currentSession) {
					instituteIdSessionIdMap.put(instituteId, academicSession.getAcademicSessionId());
					break;
				}
			}
		}
		return instituteIdSessionIdMap;
	}

	public Standard getStandardByStandardId(int instituteId, Integer academicSessionId, UUID standardId) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		if (academicSessionId <= 0 || academicSessionId == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id."));
		}
		if (standardId == null ) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid standard id."));
		}

		return instituteDao.getStandardByStandardId(instituteId, academicSessionId, standardId);
	}

	public Map<UUID, Set<Integer>> getStandardWithStaffDetailsList(int instituteId, int academicSessionId, UUID staffId, UUID standardId) {
		if(instituteId <= 0) {
			logger.error("Invalid institute id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id"));
		}

		if(academicSessionId <= 0) {
			logger.error("Invalid academic session id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id"));
		}

		final List<StandardWithStaffDetails> standardWithStaffDetailsList = instituteDao.getStandardWithStaffDetailsList(instituteId, academicSessionId, staffId, standardId);

		Collections.sort(standardWithStaffDetailsList);

		Map<UUID, Set<Integer>> classTeacherStandardSectionDetails = new HashMap<>();
		if (!CollectionUtils.isEmpty(standardWithStaffDetailsList)) {
			for (StandardWithStaffDetails standardWithStaffDetails : standardWithStaffDetailsList) {
				if(standardWithStaffDetails == null || standardWithStaffDetails.getStandardRowDetails() == null || standardWithStaffDetails.getStandardRowDetails().getStandardId() == null) {
					continue;
				}
				UUID currentLoopStandardId = standardWithStaffDetails.getStandardRowDetails().getStandardId();
				if (!classTeacherStandardSectionDetails.containsKey(currentLoopStandardId)) {
					classTeacherStandardSectionDetails.put(currentLoopStandardId,
							new HashSet<>());
				}
				if (standardWithStaffDetails.getStandardRowDetails().getSectionId() != null && standardWithStaffDetails.getStandardRowDetails().getSectionId() > 0) {
					classTeacherStandardSectionDetails.get(currentLoopStandardId).add(
							standardWithStaffDetails.getStandardRowDetails().getSectionId());
				}
			}
		}

		return classTeacherStandardSectionDetails;
	}

	public List<String> cloneClassTeacher(CloneStandardWithStaffDetailsPayload cloneStandardWithStaffDetailsPayload){
		List<String> errorMessage = new ArrayList<>();
		if(!cloneStandardWithStaffDetails(cloneStandardWithStaffDetailsPayload.getSrcInstituteId(), cloneStandardWithStaffDetailsPayload.getDestInstituteId(), cloneStandardWithStaffDetailsPayload.getSrcAcademicSessionId(), cloneStandardWithStaffDetailsPayload.getDestAcademicSessionId(), errorMessage, true)){
			errorMessage.add("Not able to Clone the Class Teacher");
		}
		return errorMessage;
	}

	private boolean cloneStandardWithStaffDetails(int srcInstituteId, int destInstituteId, int srcAcademicSessionId, int destAcademicSessionId, List<String> errorMessages,
												  boolean throwError){
		if(srcInstituteId <= 0 || destInstituteId <= 0){
			if(throwError) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id"));
			}
		}
		if(srcAcademicSessionId <= 0 || destAcademicSessionId <= 0) {
			logger.error("Invalid academic session id");
			if(throwError) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id"));
			}
		}
		List<StandardWithStaffDetails> standardWithStaffDetailsList = getStandardWithStaffDetailsList(srcInstituteId, srcAcademicSessionId, null);
		if(CollectionUtils.isEmpty(standardWithStaffDetailsList)){
			errorMessages.add("No data Found for the Standard Class Teacher. Cloning Class Teacher Is stopped");
			return true;
		}
		if(srcInstituteId != destInstituteId && !isSameOrganization(srcInstituteId, destInstituteId)){
			errorMessages.add("Destination Institute Id don't belong to same Organisation So cloning is not possible");
			return false;
		}
		Set<UUID> staffId = getStaffIdFromStandardWithStaffDetails(standardWithStaffDetailsList);
		List<Standard> destStandard = instituteDao.getInstituteStandardDetails(destInstituteId, destAcademicSessionId);
		Map<String, UUID> standardNameAndIdMap = new HashMap<>();
		Map<String, Map<String, Integer>> sectionNameAndIdMap = new HashMap<>();
		getStandardAndSectionMap(destStandard, standardNameAndIdMap, sectionNameAndIdMap, throwError);
		if(MapUtils.isEmpty(standardNameAndIdMap)){
			if(throwError) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "No data Found for the Destination Institute Standard"));
			}
			return true;
		}
		List<Staff> staffList = staffDao.getStaff(srcInstituteId, staffId);
		Map<UUID, Staff> staffIdMap = getStaffIdMap(staffList, throwError);
		if(MapUtils.isEmpty(staffIdMap)){
			errorMessages.add("Cloning class teacher aborted because no staff details were found.");
			return true;
		}
		
		List<StandardSessionDataPayload> standardSessionDataPayloadList = new ArrayList<>();
		for(StandardWithStaffDetails standardWithStaffDetail : standardWithStaffDetailsList){
			
			final StandardRowDetails standardRowDetails = standardWithStaffDetail.getStandardRowDetails();
			final String standardDisplayName = standardRowDetails.getDisplayName();
			final String standardSectionName = standardRowDetails.getSectionName();
			final Integer sectionId = standardRowDetails.getSectionId();
			final StaffLite staffLite = standardWithStaffDetail.getStaffLite();

			if(staffLite == null){
				errorMessages.add("Skipping the Standard: "+standardDisplayName + " Section: "+standardSectionName+ " Due To Staff is not 'assigned' in the Previous session");
				continue;
			}
			final Staff staff = staffIdMap.get(staffLite.getStaffId());
			if(staff == null || staff.getStaffStatus() == StaffStatus.RELIEVED){
				errorMessages.add("Skipping the Standard: "+standardDisplayName + " Section: "+standardSectionName+ " Due To Staff is in 'Relieved' state.");
				continue;
			}
			final UUID destStandardId = standardNameAndIdMap.get(standardDisplayName);
			if (destStandardId == null) {
				errorMessages.add("Skipping the Standard: " + standardDisplayName + " Section: " + standardSectionName + " Due To Standard Name Not Found In Destination Institute.");
				continue;
			}
			Integer destSectionSessionMappedId = null;
			if (sectionId != null) {
				final Map<String, Integer> sectionMap = sectionNameAndIdMap.get(standardDisplayName);
				if (MapUtils.isEmpty(sectionMap) || !sectionMap.containsKey(standardSectionName)) {
					errorMessages.add("Skipping the Standard: " + standardDisplayName + " Section: " + standardSectionName + " Due To Section Name Not Found In Destination Institute.");
					continue;
				}
				destSectionSessionMappedId = sectionMap.get(standardSectionName);
			}
			StandardSessionDataPayload standardSessionDataPayload = new StandardSessionDataPayload(destInstituteId, destAcademicSessionId, destStandardId, destSectionSessionMappedId, staffLite.getStaffId(), null);
			standardSessionDataPayloadList.add(standardSessionDataPayload);
		}
		if(CollectionUtils.isEmpty(standardSessionDataPayloadList)){
			errorMessages.add("Error while making standardSessionDataPayload. Cloning Class Teacher Is stopped");
			return false;
		}
		return instituteDao.upsertBulkStandardStaffAssignment(standardSessionDataPayloadList);
	}

	public void getStandardAndSectionMap(List<Standard> destStandardList, Map<String, UUID> standardNameAndIdMap, Map<String, Map<String, Integer>> sectionNameAndIdMap, boolean throwError){
		if(CollectionUtils.isEmpty(destStandardList)){
			if(throwError) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "No data Found for the Destination Institute Standard"));
			}
			return;
		}
		for(Standard destStandard : destStandardList){
			standardNameAndIdMap.putIfAbsent(destStandard.getDisplayName(), destStandard.getStandardId());
			if(CollectionUtils.isEmpty(destStandard.getStandardSectionList())){
				continue;
			}
			Map<String, Integer> sectionNameMap = new HashMap<>();
			for(StandardSections standardSections : destStandard.getStandardSectionList()){
				sectionNameMap.putIfAbsent(standardSections.getSectionName(), standardSections.getSectionId());
			}
			sectionNameAndIdMap.putIfAbsent(destStandard.getDisplayName(), sectionNameMap);
		}
	}
	private Set<UUID> getStaffIdFromStandardWithStaffDetails(List<StandardWithStaffDetails> standardWithStaffDetailsList){
		Set<UUID> staffId = new HashSet<>();
		if(CollectionUtils.isEmpty(standardWithStaffDetailsList)){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE,"No data Found for the Standard Class Teacher"));
		}
		for(StandardWithStaffDetails standardWithStaffDetail : standardWithStaffDetailsList){
			if(standardWithStaffDetail.getStaffLite() == null){
				continue;
			}
			staffId.add(standardWithStaffDetail.getStaffLite().getStaffId());
		}
		return staffId;
	}

	private Map<UUID, Staff> getStaffIdMap(List<Staff> staffList, boolean throwError){
		if(CollectionUtils.isEmpty(staffList)){
			if(throwError) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "No Staff Found For the Destination Institute"));
			}
			return null;
		}
		Map<UUID, Staff> staffIdMap = new HashMap<>();
		for(Staff staff : staffList){
			staffIdMap.put(staff.getStaffId(), staff);
		}
		return staffIdMap;
	}

	public List<StandardWithStaffDetails> getStandardWithStaffDetailsList(int instituteId, int academicSessionId, UUID standardId) {
		if(instituteId <= 0) {
			logger.error("Invalid institute id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id"));
		}

		if(academicSessionId <= 0) {
			logger.error("Invalid academic session id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id"));
		}

		return instituteDao.getStandardWithStaffDetailsList(instituteId, academicSessionId, null, standardId);
	}

	public boolean updateInstituteDetails(int instituteId, UUID userId, InstitutePayload institutePayload){
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		final Institute institute = getInstitute(instituteId);
		if (institute == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Institute does not exists"));
		}
		if(userId == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid user id."));
		}
		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.UPDATE_INSTITUTE_DETAILS);
        validatePayload(institutePayload);

		return instituteDao.updateInstitute(instituteId, userId, institutePayload);
	}

	public boolean isSameOrganization(int instituteId1, int instituteId2){
		Organisation organisation = getOrganization(instituteId1);
		if(!CollectionUtils.isEmpty(organisation.getInstitutes())){
			for(Institute institute : organisation.getInstitutes()){
				if(institute.getInstituteId() == instituteId2){
					return true;
				}
			}
		}
		return false;
	}

	private void validatePayload( InstitutePayload institutePayload){
		if(institutePayload == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute payload"));
		}
		if(StringUtils.isBlank(institutePayload.getInstituteName())){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Institute name can not be empty"));
		}
	}
	public  List<Document<InstituteDocumentType>> uploadDocument(int instituteId, InstituteDocumentType instituteDocumentType, String documentName, FileData document, UUID userId) {

		final Institute institute = getInstitute(instituteId);
		if (institute == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Institute does not exists"));
		}
		if(userId == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid user id."));
		}
		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.EDIT_INSTITUTE_IMAGES);
		List<Document<InstituteDocumentType>> instituteDocuments = institute.getInstituteDocuments();
		if (CollectionUtils.isEmpty(instituteDocuments)) {
			instituteDocuments = new ArrayList<>();
		}

		if (instituteDocuments.size() ==  InstituteDocumentType.values().length) {
			logger.error("Institute already have " +  InstituteDocumentType.values().length
					+ " documents uploaded. Please delete some to upload more.");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COUNT_OF_DOCUMENT,
					"Institute already have " +  InstituteDocumentType.values().length
							+ " documents uploaded. Please delete some to upload more."));
		}

		final double length = document.getContent().length / DocumentUtils.ONE_KB;
		if (length > DocumentUtils.LOGO_FILE_SIZE_LIMIT) {
			logger.error("Size {} Greater than " + DocumentUtils.LOGO_FILE_SIZE_LIMIT + " kb", length);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SIZE_OF_DOCUMENT,
					"Size Of document cannot be greater than " + DocumentUtils.LOGO_FILE_SIZE_LIMIT + " kb"));
		}

		if (!DocumentUtils.validInstituteDocument(institute, instituteDocumentType, documentName, document)) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT,
					"Document already exists with give type and name"));
		}

		/**
		 * upload document
		 */
		Document<InstituteDocumentType> uploadedDocuments = uploadedDocument(instituteId, document, instituteDocumentType, documentName);
		if(uploadedDocuments != null) {
			instituteDocuments.add(uploadedDocuments);
		}
//	    we do not need tp invalidate asset cache because it is already empty at current s3 path
		return instituteDao.updateDocuments(instituteId, instituteDocuments) ? instituteDocuments : null;
	}

	private Document<InstituteDocumentType> uploadedDocument(int instituteId, FileData fileData, InstituteDocumentType instituteDocumentType, String documentName) {
		final UUID documentId = UUID.randomUUID();
		String fileExtension = FilenameUtils.getExtension(fileData.getFileName());
		Document<InstituteDocumentType> newDocument = new Document<>(instituteDocumentType, documentName, documentId,
				fileExtension, (int) (System.currentTimeMillis() / 1000l));
		String s3Path = buildDocumentPath(instituteId, newDocument, instituteDocumentType);
		// we do not want prefix that is why we are passing null
		boolean documentUploaded = documentManager.uploadDocument(s3Path, instituteId, fileData, false);

		if (!documentUploaded) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT,
					"Unable to upload document. Please try again"));
		}
		newDocument.setS3Path(s3Path);
		return newDocument;
	}

	private String buildDocumentPath(int instituteId, Document<InstituteDocumentType> instituteDocument, InstituteDocumentType instituteDocumentType) {

		final StringBuilder s3Path = new StringBuilder();
		s3Path.append("institute-documents").append(DocumentUtils.S3_FILE_PATH_DELIMITER).append("institute_id=").append(instituteId).append(DocumentUtils.S3_FILE_PATH_DELIMITER).append("document_type=")
				.append(instituteDocumentType)
				.append(DocumentUtils.S3_FILE_PATH_DELIMITER).append(instituteDocument.getDocumentId())
				.append(".").append(instituteDocument.getFileExtension());

		return s3Path.toString();
	}

	public DownloadDocumentWrapper<Document<InstituteDocumentType>> downloadDocument(int instituteId, UUID documentId) {
		if (documentId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Invalid document Information"));
		}
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse
					(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		final Institute institute = getInstitute(instituteId);
		if (institute == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Institute does not exists"));
		}

		final List<Document<InstituteDocumentType>> instituteDocuments = institute.getInstituteDocuments();
		if (CollectionUtils.isEmpty(instituteDocuments)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Document does not exists"));
		}
		for (final Document<InstituteDocumentType> instituteDocument : instituteDocuments) {
			if (documentId.equals(instituteDocument.getDocumentId())) {
				final String s3Path = buildDocumentPath(instituteId, instituteDocument, instituteDocument.getDocumentType());
				return new DownloadDocumentWrapper<>(instituteDocument,
						documentManager.downloadDocument(instituteId, s3Path, false));
			}
		}
		return null;
	}
	public boolean deleteDocument(int instituteId,  UUID documentId, UUID userId) {
		if(instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id"));
		}

		if(userId == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid user id."));
		}
		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.EDIT_INSTITUTE_IMAGES);
		return deleteDocument(instituteId,  documentId);
	}
	public boolean deleteDocument(int instituteId,  UUID documentId) {
		if (documentId == null || instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Invalid document Information"));
		}

		final Institute institute = getInstitute(instituteId);
		if (institute == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Institute does not exists"));
		}

		final List<Document<InstituteDocumentType>> instituteDocuments = institute.getInstituteDocuments();
		if (CollectionUtils.isEmpty(instituteDocuments)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Document does not exists"));
		}
		final Iterator<Document<InstituteDocumentType>> iterator = instituteDocuments.iterator();
		boolean deleted = false;
		while (iterator.hasNext()) {
			final Document<InstituteDocumentType> instituteDocument = iterator.next();
			if (documentId.equals(instituteDocument.getDocumentId())) {
				final String s3Path = buildDocumentPath(instituteId, instituteDocument, instituteDocument.getDocumentType());
				if (documentManager.deleteDocument(instituteId, s3Path, false)) {
					iterator.remove();
					deleted = true;
					assetProvider.invalidateInstituteAssetCache(instituteId, s3Path);
				}
				break;
			}
		}
		if (!deleted) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Could not delete documents"));
		}

		return instituteDao.updateDocuments(instituteId, instituteDocuments);
	}

	public List<StandardSessionDataPayload> getStandardSessionDataPayloadList(int instituteId, int academicSessionId, UUID standardId, Set<Integer> sectionIdSet, UUID staffId) {
		if (instituteId <= 0) {
			logger.error("Invalid institute id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id"));
		}
		if (academicSessionId <= 0) {
			logger.error("Invalid academic session id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id"));
		}
		return instituteDao.getStandardSessionDataPayloadList(instituteId, academicSessionId, standardId, sectionIdSet, staffId);
	}

	public boolean updateInstituteActiveStatus(int instituteId, UUID userId, boolean isActive, boolean skipAuth) {
		if (instituteId <= 0) {
			logger.error("Invalid institute id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id"));
		}

		if (!skipAuth) {
			if (userId == null) {
				logger.error("Invalid user id");
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id"));
			}

			userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.UPDATE_INSTITUTE_ACTIVE_STATUS);

		}

		return instituteDao.updateInstituteActiveStatus(instituteId, isActive);
	}

	public UUID addInstituteBankAccount(int instituteId, UUID userId, InstituteBankAccountDetails instituteBankAccountDetailsPayload) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id"));
		}
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.EDIT_INSTITUTE_BANK_DETAILS);

		List<InstituteBankAccountDetails> existingInstituteBankAccount = instituteDao.getInstituteBankAccounts(instituteId);

		UUID existingIsPrimary= validateInstituteBankAccountDetailsPayload(instituteBankAccountDetailsPayload, false, existingInstituteBankAccount);

		try {
			final UUID bankAccountId = transactionTemplate.execute(new TransactionCallback<UUID>() {
				@Override
				public UUID doInTransaction(TransactionStatus transactionStatus) {
					boolean primaryStatus = updateInstituteBankAccountPrimaryStatus(instituteBankAccountDetailsPayload.getInstituteId(), existingIsPrimary);
					if (!primaryStatus) {
						throw new ApplicationException(
								new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Another bank account is already marked as primary. Please unset the primary status from the existing account before setting a new one."));
					}

					return instituteDao.addInstituteBankAccount(instituteBankAccountDetailsPayload, userId);
				}
			});
			return bankAccountId;
		} catch (Exception e) {
			logger.error("Unable to add institute bank account for institute {}", instituteId, e);
		}
		return null;
	}

	private UUID validateInstituteBankAccountDetailsPayload(InstituteBankAccountDetails instituteBankAccountDetailsPayload, boolean update, List<InstituteBankAccountDetails> existingInstituteBankAccount) {

		if (instituteBankAccountDetailsPayload == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Empty bank account payload."));
		}

		if (instituteBankAccountDetailsPayload.getInstituteId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if(update) {
			if (instituteBankAccountDetailsPayload.getAccountId() == null) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid account."));
			}
		}

		if (StringUtils.isBlank(instituteBankAccountDetailsPayload.getBankName())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid bank name."));
		}

		UUID existingIsPrimary= null;
		for (InstituteBankAccountDetails instituteBankAccountDetails : existingInstituteBankAccount) {
			boolean isEqualAccountId = UUIDUtils.equals(instituteBankAccountDetailsPayload.getAccountId(),instituteBankAccountDetails.getAccountId());
			if (!isEqualAccountId && !StringUtils.isEmpty(instituteBankAccountDetailsPayload.getAccountNumber()) && StringHelper.equals(instituteBankAccountDetailsPayload.getAccountNumber(), instituteBankAccountDetails.getAccountNumber())) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Institute Bank account already exists with given account number. "
								+ "Please change account number and try again."));
			}
			if(!isEqualAccountId && instituteBankAccountDetailsPayload.isPrimary() && instituteBankAccountDetails.isPrimary()){
				existingIsPrimary = instituteBankAccountDetails.getAccountId();
			}
		}
		return existingIsPrimary;
	}

	public boolean updateInstituteBankAccountPrimaryStatus(int instituteId, UUID existingIsPrimary) {
		if (existingIsPrimary == null) {
			return true;
		}
		return instituteDao.updateInstituteBankAccountPrimaryStatus(instituteId, existingIsPrimary, false);
	}

	public List<InstituteBankAccountDetails> getInstituteBankAccounts(int instituteId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id"));
		}
		return instituteDao.getInstituteBankAccounts(instituteId);
	}

	public InstituteBankAccountDetails getInstituteBankAccount(int instituteId, UUID accountId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id"));
		}
		if (accountId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid account"));
		}
		return instituteDao.getInstituteBankAccount(instituteId, accountId);
	}

	public Map<UUID, InstituteBankAccountDetails> getInstituteBankAccounts(int instituteId, Set<UUID> accountId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id"));
		}
		if (accountId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid account"));
		}
		List<InstituteBankAccountDetails> instituteBankAccountDetailsList= instituteDao.getInstituteBankAccounts(instituteId, accountId);

//		HashMap<UUID, InstituteBankAccountDetails> instituteBankAccountDetailsHashMap = new HashMap<>();
//		for (InstituteBankAccountDetails instituteBankAccountDetails : instituteBankAccountDetailsList) {
//			instituteBankAccountDetailsHashMap.put(instituteBankAccountDetails.getAccountId(), instituteBankAccountDetails);
//		}

		return EMapUtils.getMap(instituteBankAccountDetailsList, new EMapUtils.MapFunction<InstituteBankAccountDetails, UUID, InstituteBankAccountDetails>() {
			@Override
			public UUID getKey(InstituteBankAccountDetails entry) {
				return entry.getAccountId();
			}
			@Override
			public InstituteBankAccountDetails getValue(InstituteBankAccountDetails entry) {
				return entry;
			}
		});
	}

	public boolean updateInstituteBankAccountDetails(int instituteId, UUID userId, InstituteBankAccountDetails instituteBankAccountDetailsPayload, UUID accountId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id"));
		}
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.EDIT_INSTITUTE_BANK_DETAILS);

		List<InstituteBankAccountDetails> existingInstituteBankAccount = instituteDao.getInstituteBankAccounts(instituteId);

		UUID existingIsPrimary= validateInstituteBankAccountDetailsPayload(instituteBankAccountDetailsPayload, false, existingInstituteBankAccount);
		try {
			return transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus transactionStatus) {
					boolean primaryStatus = updateInstituteBankAccountPrimaryStatus(instituteBankAccountDetailsPayload.getInstituteId(), existingIsPrimary);
					if (!primaryStatus) {
						throw new ApplicationException(
								new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Another bank account is already marked as primary. Please unset the primary status from the existing account before setting a new one."));
					}

					return instituteDao.updateInstituteBankAccountDetails(instituteBankAccountDetailsPayload);
				}
			});
		} catch (Exception e) {
			logger.error("Unable to add institute bank account for institute {}", instituteId, e);
		}
		return false;
	}


	public boolean updateActiveStatusOfBankAccount(int instituteId, UUID userId, UUID accountId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id"));
		}
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id"));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.EDIT_INSTITUTE_BANK_DETAILS);

		if (accountId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid account"));
		}

		return instituteDao.updateActiveStatusOfBankAccount(instituteId, accountId, BankAccountStatus.INACTIVE);
	}

}
