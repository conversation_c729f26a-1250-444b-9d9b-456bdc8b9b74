package com.lernen.cloud.data.server.examination;

import com.embrate.cloud.core.api.examination.config.StandardMetadataPayloadReadable;
import com.embrate.cloud.core.api.examination.management.GradeRenamePayload;
import com.embrate.cloud.core.api.examination.management.GradeUpdatePayload;
import com.embrate.cloud.core.api.examination.management.StandardGradeDetails;
import com.embrate.cloud.core.api.examination.utility.ExamStructureWrapper;
import com.embrate.cloud.core.api.examination.utility.ExaminationGradesPayloadReadable;
import com.embrate.cloud.core.api.examination.utility.StandardExaminationGrades;
import com.embrate.cloud.core.api.student.management.StudentManagementFieldDetails;
import com.embrate.cloud.core.lib.hpc.HPCFormManager;
import com.embrate.cloud.lambda.utils.examination.ExamGreenSheetLambdaHandler;
import com.embrate.cloud.lambda.utils.examination.ExamReportLambdaServiceProvider;
import com.embrate.cloud.lambda.utils.pdf.admitcard.AdmitCardLambdaHandler;
import com.embrate.cloud.push.notifications.handler.StudentReportCardPublishPushNotificationHandler;
import com.lernen.cloud.core.api.admitcard.AdmitCardType;
import com.lernen.cloud.core.api.common.DeliveryMode;
import com.lernen.cloud.core.api.common.SearchResultWithPagination;
import com.lernen.cloud.core.api.common.StringHelper;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.*;
import com.lernen.cloud.core.api.examination.report.*;
import com.lernen.cloud.core.api.examination.report.greensheet.*;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.notification.NotificationBatchDetails;
import com.lernen.cloud.core.api.notification.NotificationDetails;
import com.lernen.cloud.core.api.notification.NotificationType;
import com.lernen.cloud.core.api.report.ReportOutput;
import com.lernen.cloud.core.api.sms.CustomNotificationPayload;
import com.lernen.cloud.core.api.sms.SMSResponse;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentSortingParameters;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.examination.*;
import com.lernen.cloud.core.lib.notifications.NotificationManager;
import com.lernen.cloud.pdf.admitcard.AdmitCardHandler;
import com.lernen.cloud.pdf.datesheet.DatesheetHandler;
import com.lernen.cloud.pdf.exam.reports.ExamReportSerivceProvider;
import com.lernen.cloud.sms.handler.ExamSMSHandler;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.*;

/**
 *
 * <AUTHOR>
 *
 */

@Path("/2.0/examination")
public class ExaminationEndPoint {
	private static final Logger logger = LogManager.getLogger(ExaminationEndPoint.class);

	private final ExaminationManager examinationManager;
	private final HPCFormManager hpcFormManager;
	private final ExamGreenSheetManager examGreenSheetManager;
	private final ExamReportCardManager examReportCardManager;
	private final ExamReportSerivceProvider examReportSerivceProvider;
	private final AdmitCardHandler admitCardHandler;
	private final ExamGreenSheetHandler examGreenSheetHandler;
	private final ExaminationStructureHelper examinationStructureHelper;
	private final ExamSMSHandler examSMSHandler;
	private final NotificationManager notificationManager;
	private final DatesheetHandler datesheetHandler;

	private final ExamReportLambdaServiceProvider examReportLambdaServiceProvider;
	private final AdmitCardLambdaHandler admitCardLambdaHandler;

	private final ExamGreenSheetLambdaHandler examGreenSheetLambdaHandler;
	private final StudentReportCardPublishPushNotificationHandler studentReportCardPublishPushNotificationHandler;

	public ExaminationEndPoint(ExaminationManager examinationManager,  HPCFormManager hpcFormManager, ExamGreenSheetManager examGreenSheetManager,
							   ExamReportCardManager examReportCardManager, ExamReportSerivceProvider examReportSerivceProvider,
							   AdmitCardHandler admitCardHandler, ExamGreenSheetHandler examGreenSheetHandler,
							   ExaminationStructureHelper examinationStructureHelper, ExamSMSHandler examSMSHandler,
							   NotificationManager notificationManager,
							   DatesheetHandler datesheetHandler, ExamReportLambdaServiceProvider examReportLambdaServiceProvider,
							   AdmitCardLambdaHandler admitCardLambdaHandler, ExamGreenSheetLambdaHandler examGreenSheetLambdaHandler,
							   StudentReportCardPublishPushNotificationHandler studentReportCardPublishPushNotificationHandler) {
		this.examinationManager = examinationManager;
		this.hpcFormManager = hpcFormManager;
		this.examGreenSheetManager = examGreenSheetManager;
		this.examReportCardManager = examReportCardManager;
		this.examReportSerivceProvider = examReportSerivceProvider;
		this.admitCardHandler = admitCardHandler;
		this.examGreenSheetHandler = examGreenSheetHandler;
		this.examinationStructureHelper = examinationStructureHelper;
		this.examSMSHandler = examSMSHandler;
		this.notificationManager = notificationManager;
		this.datesheetHandler = datesheetHandler;
		this.examReportLambdaServiceProvider = examReportLambdaServiceProvider;
		this.admitCardLambdaHandler = admitCardLambdaHandler;
		this.examGreenSheetLambdaHandler = examGreenSheetLambdaHandler;
		this.studentReportCardPublishPushNotificationHandler = studentReportCardPublishPushNotificationHandler;
	}

	@GET
	@Path("dimension/institute/{institute_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getExamDimensions(@PathParam("institute_id") int instituteId) {
		final List<ExamDimension> examDimensions = examinationManager.getExamDimensions(instituteId);
		if (examDimensions == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION, "Unable to get dimensions."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(examDimensions).build();
	}

	@POST
	@Path("dimension")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response createExamDimension(ExamDimension dimensionPayload, @QueryParam("institute_id") int instituteId) {
		if (!examinationManager.createExamDimension(instituteId, dimensionPayload)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION, "Unable to add dimension."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@PUT
	@Path("dimension")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateExamDimension(ExamDimension dimensionPayload, @QueryParam("institute_id") int instituteId) {
		if (!examinationManager.updateExamDimension(instituteId, dimensionPayload)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION, "Unable to update dimension."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@DELETE
	@Path("dimension/{dimension_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response deleteExamDimension(@PathParam("dimension_id") int dimensionId,
										@QueryParam("institute_id") int instituteId) {
		if (!examinationManager.deleteExamDimension(instituteId, dimensionId)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION, "Unable to delete dimension."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@POST
	@Path("bulk-standard-grading")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response addBulkStandardGrading(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId, StandardMetadataPayloadReadable standardMetadataPayloadReadable) {
		if (!examinationManager.addBulkStandardGrading(instituteId, userId, standardMetadataPayloadReadable)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION, "Unable to add standard grading."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@POST
	@Path("bulk-standard-grading-scheme")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response addBulkStandardGradingScheme(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId, @QueryParam("academic_session_id") int academicSessionId, ExaminationGradesPayloadReadable examinationGradesPayloadReadable) {
		if (!examinationManager.addBulkStandardGradingScheme(instituteId, userId, academicSessionId, examinationGradesPayloadReadable)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION, "Unable to add standard grading scheme."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@POST
	@Path("bulk-grading-scheme")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response bulkStandardGradingScheme(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId, @QueryParam("academic_session_id") int academicSessionId, GradeUpdatePayload gradeUpdatePayload) {
		if (!examinationManager.bulkStandardGradingScheme(instituteId, academicSessionId, userId, gradeUpdatePayload)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION, "Unable to add standard grading scheme."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@PUT
	@Path("bulk-standards-metadata")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response bulkUpsertStandardsMetadata(@QueryParam("institute_id") int instituteId,
												@QueryParam("academic_session_id") int academicSessionId,
												@QueryParam("user_id") UUID userId,
												StandardMetadataBulkPayload payload) {
		final boolean success = examinationManager.bulkUpsertStandardsMetadata(instituteId, academicSessionId, userId, payload);
		if (!success) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION, "Unable to update standards metadata."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@GET
	@Path("{exam_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getExamDetails(@PathParam("exam_id") UUID examId, @QueryParam("institute_id") int instituteId) {
		final ExamDetails examDetails = examinationManager.getExamDetails(examId, instituteId);
		if (examDetails == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION, "Unable to get exam details."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(examDetails).build();
	}

	@POST
	@Path("create")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response createExam(ExamCreationPayload examCreationPayload, @QueryParam("institute_id") int instituteId,
							   @QueryParam("user_id") UUID userId) {
		final UUID examId = examinationManager.createExam(examCreationPayload, instituteId, userId, true);
		if (examId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION, "Unable to add exam."));
		}
		final Map<String, UUID> response = new HashMap<>();
		response.put("examId", examId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(response).build();
	}

	@PUT
	@Path("update-exam-metadata")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateExamMetaData(ExamUpdatePayload examUpdatePayload,
									   @QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId) {
		final boolean updated = examinationManager.updateExamMetaData(examUpdatePayload, instituteId, userId);
		if (!updated) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Unable to update exam details."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@DELETE
	@Path("delete/{exam_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response deleteExam(@PathParam("exam_id") UUID examId, @QueryParam("institute_id") int instituteId,
							   @QueryParam("user_id") UUID userId) {
		if (!examinationManager.deleteExam(instituteId, examId, userId)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION, "Unable to delete exam."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@POST
	@Path("{exam_id}/assign-courses/{course_type}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response addCoursesInExam(List<ExamCoursesPayload> examCoursesPayloads, @PathParam("exam_id") UUID examId,
									 @PathParam("course_type") CourseType courseType, @QueryParam("institute_id") int instituteId,
									 @DefaultValue("true") @QueryParam("update") Boolean update, @QueryParam("user_id") UUID userId) {
		if (!examinationManager.addCoursesInExam(instituteId, examId, courseType, examCoursesPayloads, update, userId, true)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Unable to add exam courses"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@GET
	@Path("standard/{standard_id}/assigned-courses")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getExamCourses(@PathParam("standard_id") UUID standardId,
								   @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
								   @QueryParam("only_leaf_exams") boolean onlyLeafExams, @QueryParam("is_filtered_dimension") boolean isFilteredDimension) {
		final List<ExamCoursesData> examCoursesDatas = examinationManager.getExamCourses(instituteId, academicSessionId, standardId, onlyLeafExams, isFilteredDimension);
		return Response.status(Response.Status.OK.getStatusCode()).entity(examCoursesDatas).build();
	}

	@GET
	@Path("standard/{standard_id}/forest")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getExamsForest(@PathParam("standard_id") UUID standardId,
								   @QueryParam("academic_session_id") int academicSessionId, @QueryParam("institute_id") int instituteId) {
		final List<ExamNode> examForest = examinationManager.getClassExamsForest(standardId, academicSessionId,
				instituteId, false);
		if (examForest == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION, "Unable to get exams details."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(examForest).build();
	}

	@PUT
	@Path("update-exam-dimension/{exam_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateExamDimensionMapping(@PathParam("exam_id") UUID examId,
											   @QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
											   Map<CourseType, List<ExamDimensionValuesPayload>> courseTypeDimensions) {
		final boolean updatedMapping = examinationManager.updateExamDimensionMapping(instituteId, examId,
				courseTypeDimensions, userId);
		if (!updatedMapping) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Unable to update exam dimension mapping."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@Deprecated
	@GET
	@Path("student/{student_id}/exam/{exam_id}/course/{course_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getMarksByCourseId(@QueryParam("institute_id") int instituteId,
									   @PathParam("student_id") UUID studentId, @PathParam("exam_id") UUID examId,
									   @PathParam("course_id") UUID courseId) {
		final List<MarksFeedData> marksFeeding = examinationManager.getMarksByCourseId(instituteId, studentId, examId,
				courseId);
		if (marksFeeding == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Unable to marks for this course."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(marksFeeding).build();
	}

	@GET
	@Path("{exam_id}/course/{course_id}/marks")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getClassMarksByCourseId(@QueryParam("institute_id") int instituteId,
											@QueryParam("academic_session_id") int academicSessionId,
											@PathParam("exam_id") UUID examId, @PathParam("course_id") UUID courseId,
											@QueryParam("standard_id") UUID standardId,
											@QueryParam("section_id") String sectionIdStr,
											@QueryParam("add_relieved_students") boolean addRelievedStudents, @QueryParam("user_id") UUID userId) {

		final Integer sectionId = getSectionId(sectionIdStr);

		final List<StudentExamMarksDetails> studentExamMarksDetails = examinationManager
				.getClassMarksByCourseId(instituteId, academicSessionId, examId, courseId, standardId, sectionId, addRelievedStudents, userId);
		if (studentExamMarksDetails == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Unable to get marks for this course."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentExamMarksDetails).build();
	}

	@GET
	@Path("standard/{standard_id}/exam/{exam_id}/course/{course_id}/marks")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getClassMarksByCourseExamDimensionIds(@PathParam("standard_id") UUID standardId, @PathParam("exam_id") UUID examId,
														  @PathParam("course_id") UUID courseId, @QueryParam("institute_id") int instituteId,
														  @QueryParam("academic_session_id") int academicSessionId, @QueryParam("section_id") String sectionIdStr,
														  @QueryParam("dimension_id") int dimensionId, @QueryParam("course_type") CourseType courseType,
														  @QueryParam("add_relieved_students") boolean addRelievedStudents, @QueryParam("user_id") UUID userId,
														  @QueryParam("student_sorting_parameters") StudentSortingParameters studentSortingParameters) {

		final Integer sectionId = getSectionId(sectionIdStr);
		final ExamDimensionStudentMarksDetails examDimensionStudentMarksDetails = examinationManager
				.getClassMarksByCourseExamDimensionIds(instituteId, academicSessionId, standardId, sectionId,
						courseType, courseId, examId, dimensionId, addRelievedStudents, userId, studentSortingParameters);
		return Response.status(Response.Status.OK.getStatusCode()).entity(examDimensionStudentMarksDetails).build();
	}
	@GET
	@Path("standard/{standard_id}/exam/{exam_id}/course/{course_id}/dimension-with-marks")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getClassMarksByCourseExamDimensionIdsList(@PathParam("standard_id") UUID standardId, @PathParam("exam_id") UUID examId,
														  @PathParam("course_id") UUID courseId, @QueryParam("institute_id") int instituteId,
														  @QueryParam("academic_session_id") int academicSessionId, @QueryParam("section_id") String sectionIdStr,
														  @QueryParam("dimension_id_str") String dimensionIdStr, @QueryParam("course_type") CourseType courseType,
														  @QueryParam("add_relieved_students") boolean addRelievedStudents, @QueryParam("user_id") UUID userId) {

		final Integer sectionId = getSectionId(sectionIdStr);
		Set<Integer> dimensionIdSet = StringHelper.convertStringToSetInteger(dimensionIdStr);
		final ExamDimensionDataStudentMarksDetails examDimensionListStudentMarksDetails = examinationManager
				.getClassMarksByCourseExamDimensionIdsList(instituteId, academicSessionId, standardId, sectionId,
						courseType, courseId, examId, dimensionIdSet, addRelievedStudents, userId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(examDimensionListStudentMarksDetails).build();
	}

	@GET
	@Path("{exam_id}/marks")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getClassMarksByCourseList(@PathParam("exam_id") UUID examId, @QueryParam("institute_id") int instituteId,
											  @QueryParam("course_id") String courseIdsStr, @QueryParam("section_id") String sectionIdStr,
											  @QueryParam("add_relieved_students") boolean addRelievedStudents,
											  @QueryParam("academic_session_id") int academicSessionId, @QueryParam("standard_id") UUID standardId,
											  @QueryParam("user_id") UUID userId) {

		final Integer sectionId = getSectionId(sectionIdStr);

		final Set<UUID> courseIdSet = StringHelper.convertStringToSetUUID(courseIdsStr);

		final List<StudentExamMarksDetails> studentExamMarksDetails = examinationManager
				.getClassMarksByCourseList(instituteId, academicSessionId, examId, courseIdSet, standardId, sectionId, addRelievedStudents, userId);
		if (studentExamMarksDetails == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Unable to get marks for this course."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentExamMarksDetails).build();
	}

	@GET
	@Path("{exam_id}/effective-marks")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getResolvedClassMarks(@QueryParam("institute_id") int instituteId,
										  @PathParam("exam_id") UUID examId) {
		final List<StudentExamMarksDetails> studentExamMarksDetails = examinationManager
				.getResolvedClassMarks(instituteId, examId, null, null, false);
		if (studentExamMarksDetails == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Unable to get marks for this course."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentExamMarksDetails).build();
	}

	@GET
	@Path("{exam_id}/course/{course_id}/effective-marks")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getResolvedClassMarks(@QueryParam("institute_id") int instituteId,
										  @PathParam("exam_id") UUID examId, @PathParam("course_id") UUID courseId,
										  @QueryParam("section_id") String sectionIdStr, @QueryParam("add_relieved_students") boolean addRelievedStudents,
										  @QueryParam("standard_id") UUID standardId, @QueryParam("academic_session_id") int academicSessionId, @QueryParam("user_id") UUID userId) {
		final Integer sectionId = getSectionId(sectionIdStr);
		final List<StudentExamMarksDetails> studentExamMarksDetails = examinationManager
				.getResolvedClassMarks(instituteId, academicSessionId, examId, courseId, standardId, sectionId, addRelievedStudents, userId);
		if (studentExamMarksDetails == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Unable to get marks for this course."));
		}
//		// TODO: hack to remove the other courses and only keep requested course
//		for (final StudentExamMarksDetails studentExamMarksDetailsEntry : studentExamMarksDetails) {
//			final Iterator<ExamCourseMarks> iterator = studentExamMarksDetailsEntry.getExamCoursesAllDimensionsMarks()
//					.iterator();
//			while (iterator.hasNext()) {
//				final ExamCourseMarks examCourseMarks = iterator.next();
//				if (!examCourseMarks.getCourse().getCourseId().equals(courseId)) {
//					iterator.remove();
//				}
//			}
//		}

		return Response.status(Response.Status.OK.getStatusCode()).entity(studentExamMarksDetails).build();
	}

	@GET
	@Path("standard/{standard_id}/marks-feed-structure")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getClassMarksFeedStructure(@PathParam("standard_id") UUID standardId,
											   @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {
		final List<CourseMarksFeedExams> courseMarksFeedExams = examinationManager
				.getClassMarksFeedStructure(standardId, instituteId, academicSessionId);
		if (courseMarksFeedExams == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Unable to get marks structure for this course."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(courseMarksFeedExams).build();
	}

	@GET
	@Path("standard/{standard_id}/marks-feed-structure-dimensions")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getClassMarksFeedStructureWithDimensions(@PathParam("standard_id") UUID standardId,
															 @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {
		final List<CourseMarksFeedExamWithDimensions> courseMarksFeedExams = examinationManager
				.getClassMarksFeedStructureWithDimensions(standardId, instituteId, academicSessionId);
		if (courseMarksFeedExams == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Unable to get marks structure for this course."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(courseMarksFeedExams).build();
	}
	@GET
	@Path("standard/{standard_id}/marks-feed-structure-dimensions/filter")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getClassMarksFeedStructureWithDimensionsFilter(@PathParam("standard_id") UUID standardId,
															 @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {
		final List<CourseMarksFeedExamWithDimensionsFilter> courseMarksFeedExamsDimensionsFilters = examinationManager
				.getClassMarksFeedStructureWithDimensionsFilter(standardId, instituteId, academicSessionId);
		if (courseMarksFeedExamsDimensionsFilters == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Unable to get marks structure for this course."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(courseMarksFeedExamsDimensionsFilters).build();
	}
	@GET
	@Path("standard/{standard_id}/course-exam-dimensions-details")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getCourseExamDimensionDetails(@PathParam("standard_id") UUID standardId,
												  @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {
		final List<CourseExamDimensionDetails> courseMarksFeedExams = examinationManager
				.getCourseExamDimensionDetails(standardId, instituteId, academicSessionId);
		if (courseMarksFeedExams == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Unable to get marks structure for this course."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(courseMarksFeedExams).build();
	}

	@POST
	@Path("submit-marks")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response feedMarks(List<MarksFeedData> marksFeedDatas, @QueryParam("institute_id") int instituteId,
							  @QueryParam("academic_session_id") int academicSessionId,
							  @QueryParam("status") MarksFeedStatus marksFeedStatus, @QueryParam("standard_id") UUID standardId,
							  @QueryParam("section_id") String sectionIdStr, @QueryParam("user_id") UUID userId) {
		final Integer sectionId = getSectionId(sectionIdStr);

		if (!examinationManager.feedMarks(instituteId, academicSessionId, marksFeedDatas, marksFeedStatus, standardId, sectionId, userId)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_MARKS_ADDED, "Unable to update marks."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@GET
	@Path("{institute_id}/top-stats")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getInstituteTopStats(@PathParam("institute_id") int instituteId,
										 @QueryParam("academic_session_id") int academicSessionId) {
		final List<ClassExamMainStats> classExamMainStats = examinationManager.getInstituteTopStats(instituteId,
				academicSessionId);
		if (classExamMainStats == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION, "Unable to get marks stats"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(classExamMainStats).build();
	}

	@POST
	@Path("create-default-exam")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response createDefaultExamForStandards(@QueryParam("institute_id") int instituteId,
												  @QueryParam("academic_session_id") int academicSessionid) {
		final Boolean addedDefaultExams = examinationManager.createDefaultExamForStandards(instituteId,
				academicSessionid);
		if (addedDefaultExams == null || addedDefaultExams == Boolean.FALSE) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to add default exams."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@POST
	@Path("create-exam-structure")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response createExamStructure(@QueryParam("institute_id") int instituteId,
										ExamStructureWrapper examStructureWrapper) {
		boolean success = false;
		try {
			success = examinationStructureHelper.createExamStructure(instituteId, examStructureWrapper);
		} catch (final EmbrateRunTimeException e) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, e.getMessage()));
		}

		if (!success) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to create exam structure"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@POST
	@Path("marks-report-structure/parameters")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateExamReportStructure(@QueryParam("institute_id") int instituteId,
											  ExamReportCardConfigurationPayload examReporCardConfigurationPayload) {
		final boolean updated = examReportCardManager.updateExamReportCardConfiguration(instituteId,
				examReporCardConfigurationPayload);
		if (!updated) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to add exam report structure."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@GET
	@Path("marks-report-structure/parameters/{standard_id}/{report_type}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getExamReportStructureReadable(@PathParam("standard_id") UUID standardId,
												   @PathParam("report_type") String reportType, @QueryParam("institute_id") int instituteId,
												   @QueryParam("academic_session_id") int academicSessionId) {
		final ExamReportCardConfigurationPayload examReportCardConfigurationPayload = examReportCardManager
				.getReadableExamReportCardConfiguration(instituteId, academicSessionId, standardId, reportType);
		if (examReportCardConfigurationPayload == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Unable to get exam marks structure"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(examReportCardConfigurationPayload).build();
	}

	@POST
	@Path("marks-report-structure")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateExamReportStructure(@QueryParam("institute_id") int instituteId,
											  ExamReportCardConfiguration examReportCardConfiguration) {
		final boolean updated = examReportCardManager.updateExamReportCardConfiguration(instituteId,
				examReportCardConfiguration);
		if (!updated) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to add exam report structure."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@GET
	@Path("marks-report-structure/{standard_id}/{report_type}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getExamReportStructure(@PathParam("standard_id") UUID standardId,
										   @PathParam("report_type") String reportType, @QueryParam("institute_id") int instituteId,
										   @QueryParam("academic_session_id") int academicSessionId) {
		final ExamReportCardConfiguration examReportCardConfiguration = examReportCardManager
				.getExamReportCardConfiguration(instituteId, academicSessionId, standardId, reportType);
		if (examReportCardConfiguration == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Unable to get exam marks structure"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(examReportCardConfiguration).build();
	}

	@DELETE
	@Path("marks-report-structure/parameters/{standard_id}/{report_type}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response deleteExamReportCardConfiguration(@PathParam("standard_id") UUID standardId,
													  @PathParam("report_type") String reportType, @QueryParam("institute_id") int instituteId,
													  @QueryParam("academic_session_id") int academicSessionId) {
		boolean deleted = examReportCardManager
				.deleteExamReportCardConfiguration(instituteId, academicSessionId, standardId, reportType);
		if (!deleted) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Unable to delete exam report card structure"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@GET
	@Path("marks-report/{student_id}/{report_type}/pdf")
	@Produces("application/pdf")
	public Response getStudentMarksReport(@PathParam("student_id") UUID studentId,
										  @PathParam("report_type") String reportType, @QueryParam("institute_id") int instituteId,
										  @QueryParam("academic_session_id") int academicSessionId, @QueryParam("compute_rank") boolean computeRank,
										  @QueryParam("user_id") UUID userId, @QueryParam("user_type_str") String userTypeStr) {
		UserType userType = UserType.getUserType(userTypeStr);
		final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(instituteId, academicSessionId,
				studentId, reportType, computeRank, userId, userType);
		// Just write filename= no need for attachment/inline for displaying pdf
		if (documentOutput == null) {
			return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
		}
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@GET
	@Path("marks-report/standard/{standard_id}/{report_type}/pdf")
	@Produces("application/pdf")
	public Response getClassMarksReport(@PathParam("standard_id") UUID standardId,
										@PathParam("report_type") String reportType, @QueryParam("institute_id") int instituteId,
										@QueryParam("academic_session_id") int academicSessionId, @QueryParam("section_id") Integer sectionId,
										@QueryParam("user_id") UUID userId, @QueryParam("student_per_page") Integer studentPerPage) {
		if (studentPerPage != null) {
			final DocumentOutput documentOutput = examReportSerivceProvider.getClassExamReport(instituteId,
					academicSessionId, standardId, sectionId, reportType, userId, studentPerPage, null,
					"CLASS");
			// Just write filename= no need for attachment/inline for displaying pdf
			if (documentOutput == null) {
				return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
			}
			return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
					.header("Content-Disposition", "filename=" + documentOutput.getName())
					.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
		}

		final DocumentOutput documentOutput = examReportLambdaServiceProvider.getClassExamReport(instituteId,
				academicSessionId, standardId, sectionId, reportType, userId, null, "CLASS");

		// Just write filename= no need for attachment/inline for displaying pdf
		if (documentOutput == null) {
			return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
		}
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@POST
	@Path("bulk-marks-report/standard/{standard_id}/{report_type}/pdf")
	@Produces("application/pdf")
	public Response getBulkClassMarksReport(@PathParam("standard_id") UUID standardId, @PathParam("report_type") String reportType, @QueryParam("institute_id") int instituteId,
											@QueryParam("academic_session_id") int academicSessionId, @QueryParam("section_id") Integer sectionId,
											@QueryParam("user_id") UUID userId, List<UUID> studentIdList) {


		if (CollectionUtils.isEmpty(studentIdList)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid student Ids"));
		}

		final DocumentOutput documentOutput = examReportLambdaServiceProvider.getClassExamReport(instituteId,
				academicSessionId, standardId, sectionId, reportType, userId, studentIdList, "BULK");

		if (documentOutput == null) {
			return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
		}
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@GET
	@Path("generate-report/{report_type}")
	@Produces(MediaType.APPLICATION_OCTET_STREAM)
	public Response generateReport(@QueryParam("institute_id") int instituteId,
								   @QueryParam("academic_session_id") int academicSessionId, @PathParam("report_type") String reportType) {
		if (reportType == null || academicSessionId <= 0) {
			return Response.status(Response.Status.NOT_ACCEPTABLE.getStatusCode()).build();
		}
		final ReportOutput reportOutput = examReportCardManager.generateReport(instituteId, academicSessionId,
				reportType);
		if (reportOutput == null) {
			return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
		}
		return Response.status(Response.Status.OK.getStatusCode())
				.header("Content-Disposition", "attachment;filename=" + reportOutput.getReportName())
				.entity(new ByteArrayInputStream(reportOutput.getReportContent().toByteArray())).build();
	}

	@GET
	@Path("grades/{standard_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getExamGrades(@PathParam("standard_id") UUID standardId,
								  @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
								  @QueryParam("course_type") CourseType courseType) {

		final List<ExamGrade> examGrades = examinationManager.getExamGrades(instituteId, academicSessionId, standardId,
				courseType);
		if (examGrades == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to get exam grades"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(examGrades).build();
	}

	@GET
	@Path("grades")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStandardExamGrades(@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
								  @QueryParam("standard_name") String standardName) {

		final Map<CourseType, List<StandardExaminationGrades>> standardExamGradesMap = examinationManager.getStandardExamGrades(instituteId, academicSessionId,
				standardName);

		return Response.status(Response.Status.OK.getStatusCode()).entity(standardExamGradesMap).build();
	}

	@GET
	@Path("grade-details/{institute_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStandardGradeScheme(@PathParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {

		final List<StandardGradeDetails> standardGradeDetails = examinationManager.getGradeSchemeWithStandard(instituteId, academicSessionId);

		return Response.status(Response.Status.OK.getStatusCode()).entity(standardGradeDetails).build();
	}

	@PUT
	@Path("rename-grade")
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateStandardGradeScheme(@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
								  @QueryParam("user_id") UUID userId, GradeRenamePayload gradeRenamePayload) {

		boolean result = examinationManager.updateGradeScheme(instituteId, academicSessionId,
		userId, gradeRenamePayload);

		if(!result){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Failed to Update Grading Scheme"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@DELETE
	@Path("grade-detail")
	@Produces(MediaType.APPLICATION_JSON)
	public Response deleteStandardGradeScheme(@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
								  @QueryParam("user_id") UUID userId, @QueryParam("standard_id") List<UUID> standardIdList) {

		boolean result = examinationManager.deleteBulkStandardGradingScheme(instituteId, academicSessionId, userId, standardIdList);

		if(!result){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Failed to Delete Grading Scheme"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@GET
	@Path("grades/exam/{exam_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getExamGradesByExam(@PathParam("exam_id") UUID examId, @QueryParam("institute_id") int instituteId,
										@QueryParam("course_type") CourseType courseType) {

		final List<ExamGrade> examGrades = examinationManager.getExamGrades(instituteId, examId, courseType);
		if (examGrades == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to get exam grades"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(examGrades).build();
	}

	@GET
	@Path("reportcard-types/{standard_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getReport(@PathParam("standard_id") UUID standardId, @QueryParam("institute_id") int instituteId,
							  @QueryParam("academic_session_id") int academicSessionId) {

		final List<ExamReportCardMetadata> examReportCardMetadatas = examinationManager
				.getExamReportCardMetadatas(instituteId, academicSessionId, standardId);

		if (examReportCardMetadatas == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to get exam report card types"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(examReportCardMetadatas).build();
	}

	@GET
	@Path("report-card-types-name")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getReportCardType(@QueryParam("institute_id") int instituteId,
									  @QueryParam("academic_session_id") int academicSessionId) {

		final List<ExamReportCardBasicDetails> examReportCardBasicDetailsList = examinationManager
				.getExamReportCardBasicDetails(instituteId, academicSessionId);

		if (examReportCardBasicDetailsList == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to get exam report card types"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(examReportCardBasicDetailsList).build();
	}

	/**
	 * If passed an empty string to Integer field API fails, casting here to integer
	 *
	 * @return
	 */
	private Integer getSectionId(String sectionIdStr) {
		Integer sectionId = null;
		try {
			sectionId = Integer.parseInt(sectionIdStr);
		} catch (final Exception e) {

		}
		return sectionId;
	}

	@GET
	@Path("standard/{standard_id}/report-card-variables")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getClassList(@QueryParam("institute_id") int instituteId, @PathParam("standard_id") UUID standardId,
								 @QueryParam("section_id") String sectionIdStr, @QueryParam("academic_session_id") int academicSessionId,
								 @QueryParam("report_type") String reportType, @QueryParam("student_id") UUID studentId,
								 @QueryParam("student_sorting_parameters") StudentSortingParameters studentSortingParameters) {
		final Integer sectionId = getSectionId(sectionIdStr);
		final ReportCardVariableDetails reportCardVariableDetails = examReportCardManager.getClassList(instituteId,
				standardId, sectionId, academicSessionId, reportType, studentId, studentSortingParameters);
		if (reportCardVariableDetails == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Unable to get student report card variables for this std."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(reportCardVariableDetails).build();
	}

	@POST
	@Path("report-card-attributes")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateReportCardAttribute(ReportCardVariableDetailsPayload reportCardVariableDetails,
											  @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
											  @QueryParam("user_id") UUID userId) {
		final boolean updated = examReportCardManager.updateReportCardAttribute(instituteId, reportCardVariableDetails,
				userId);
		if (!updated) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to add report card attribute"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@POST
	@Path("report-card-attributes-lite")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response liteUpdateReportCardAttribute(ReportCardVariableDetailsPayloadLite reportCardVariableDetailsPayloadLite,
												  @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
												  @QueryParam("user_id") UUID userId) {
		final boolean updated = examReportCardManager.liteUpdateReportCardAttribute(instituteId, reportCardVariableDetailsPayloadLite,
				userId);
		if (!updated) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to add report card attribute"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@GET
	@Path("exam-metadata/session-standard-map")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getSessionExamMetadata(@QueryParam("institute_id") int instituteId) {
		final Map<Integer, Map<UUID, List<ExamMetaData>>> sessionClassExamMap = examinationManager
				.getSessionStandardExamMetadata(instituteId);
		if (sessionClassExamMap == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Unable to get exam metadata details."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(sessionClassExamMap).build();
	}

	@GET
	@Path("exam-metadata/{standard_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getExamMetadataByStandard(@PathParam("standard_id") UUID standardId, @QueryParam("institute_id") int instituteId,
											  @QueryParam("academic_session_id") int academicSessionId) {
		final List<ExamMetaData> examMetaDataList = examinationManager.getExamMetadataByStandard(instituteId, academicSessionId, standardId);
		if (examMetaDataList == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Unable to get exam metadata details."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(examMetaDataList).build();
	}

	@POST
	@Path("admit-card/exam/{exam_id}/pdf/{admit_card_type}")
	@Produces("application/pdf")
	public Response generateAdmitCards(@PathParam("exam_id") UUID examId, @QueryParam("institute_id") int instituteId,
									   @QueryParam("user_id") UUID userId, @PathParam("admit_card_type") AdmitCardType admitCardType,
									   @QueryParam("count_per_page") Integer countPerPage, List<UUID> studentUUIDs) {

		final DocumentOutput documentOutput = admitCardLambdaHandler.generateAdmitCards(instituteId, examId, userId,
				admitCardType, studentUUIDs, countPerPage);
		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@POST
	@Path("greensheet-structure")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateGreenSheetClassStructure(@QueryParam("institute_id") int instituteId,
												   GreenSheetClassStructure<GreenSheetExamDimensionMapData> greenSheetClassStructure) {
		greenSheetClassStructure.setInstituteId(instituteId);
		final boolean updated = examGreenSheetManager.addGreenSheetStructure(greenSheetClassStructure);
		if (!updated) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to add green sheet structure."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@POST
	@Path("greensheet-structure/readable")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateReadableGreenSheetClassStructure(@QueryParam("institute_id") int instituteId,
														   GreenSheetClassStructure<GreenSheetExamDimensionMapDataReadablePayload> greenSheetClassStructure) {
		greenSheetClassStructure.setInstituteId(instituteId);
		final boolean updated = examGreenSheetManager.addReadableGreenSheetStructure(greenSheetClassStructure);
		if (!updated) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to add green sheet structure."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@GET
	@Path("greensheet-structure/session/{academic_session_id}/standard/{standard_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public <T extends IGreenSheetExamDimensionMapData> Response getGreenSheetClassStructure(
			@PathParam("academic_session_id") int academicSessionId, @PathParam("standard_id") UUID standardId,
			@QueryParam("institute_id") int instituteId, @QueryParam("read_format") boolean readFormat) {
		final GreenSheetClassStructure<T> greenSheetClassStructure = examGreenSheetManager
				.getGreenSheetStructure(instituteId, academicSessionId, standardId, readFormat);

		return Response.status(Response.Status.OK.getStatusCode()).entity(greenSheetClassStructure).build();
	}

	@GET
	@Path("greensheet-data/session/{academic_session_id}/standard/{standard_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getGreenSheetClassData(@PathParam("academic_session_id") int academicSessionId,
										   @PathParam("standard_id") UUID standardId, @QueryParam("institute_id") int instituteId) {

		final GreenSheetClassData greenSheetClassData = examGreenSheetManager.getGreenSheetClassData(instituteId,
				academicSessionId, standardId);

		return Response.status(Response.Status.OK.getStatusCode()).entity(greenSheetClassData).build();
	}

	@GET
	@Path("generate-greensheet/{academic_session_id}/{standard_id}/{institute_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response generateGreenSheet(@PathParam("academic_session_id") int academicSessionId,
									   @PathParam("standard_id") UUID standardId, @PathParam("institute_id") int instituteId)
			throws FileNotFoundException, IOException {

		//for prod run
		final ReportOutput reportOutput = examGreenSheetLambdaHandler.generateGreenSheet(instituteId, academicSessionId,
				standardId);

		//for local run
//		final ReportOutput reportOutput = examGreenSheetHandler.generateGreenSheet(instituteId, academicSessionId, standardId);
		if (reportOutput == null) {
			logger.error("Null report output for instituteId {}", instituteId);
			return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
		}
		return Response.status(Response.Status.OK.getStatusCode())
				.header("Content-Disposition", "attachment;filename=" + reportOutput.getReportName())
				.entity(new ByteArrayInputStream(reportOutput.getReportContent().toByteArray())).build();
	}

	@GET
	@Path("result-declaration/exam-report-card")
	@Produces("application/pdf")
	public Response getStudentResultDeclarationExamReportCard(@QueryParam("report_card_type") String reportType,
															  @QueryParam("institute_unique_code") UUID instituteUniqueCode,
															  @QueryParam("academic_session_id") int academicSessionId,
															  @QueryParam("admission_number") String admissionNumber, @QueryParam("student_name") String studentName,
															  @QueryParam("father_name") String fatherName, @QueryParam("google_auth_key") String googleAuthKey) {

		final Student student = examinationManager.verifyAndGetStudentForExamResult(instituteUniqueCode,
				academicSessionId, admissionNumber, studentName, fatherName, googleAuthKey);

		if (student == null) {
			logger.error("Student is empty admissionNumber {}, fatherName {}, studentName {}, instituteUniqueCode {}",
					admissionNumber, fatherName, studentName, instituteUniqueCode);
			return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
		}
		final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(
				student.getStudentAcademicSessionInfoResponse().getAcademicSession().getInstituteId(),
				academicSessionId, student.getStudentId(), reportType, false, null, true);
		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@GET
	@Path("/published-exam-details/{student_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getPublishExamDetailsByStudentId(@PathParam("student_id") UUID studentId,
													 @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {
		final List<StudentExamBasicDetails> studentExamBasicDetailsList = examinationManager.getPublishExamDetailsByStudentId(
				instituteId, academicSessionId, studentId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentExamBasicDetailsList).build();
	}

	@GET
	@Path("/{student_id}/exam-details/{exam_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getSingleStudentExamMarksDetails(@PathParam("student_id") UUID studentId,
													 @PathParam("exam_id") UUID examId, @QueryParam("institute_id") int instituteId,
													 @QueryParam("academic_session_id") int academicSessionId) {
		final StudentViewExamMarksDetails studentViewExamMarksDetails = examinationManager.getSingleStudentExamMarksDetails(
				instituteId, academicSessionId, examId, studentId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentViewExamMarksDetails).build();
	}

	@POST
	@Path("bulk-sms-send/{exam_id}/{course_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response sendCustomSMSToStudents(@PathParam("exam_id") UUID examId, @PathParam("course_id") UUID courseId, @QueryParam("institute_id") int instituteId,
											@QueryParam("user_id") UUID userId, @QueryParam("send_to_absentees") boolean sendToAbsentees,
											@QueryParam("section_id") String sectionIdStr, CustomNotificationPayload studentCustomNotificationPayload) {
		final Integer sectionId = getSectionId(sectionIdStr);
		final SMSResponse smsResponse = examSMSHandler.bulkSend(instituteId, studentCustomNotificationPayload, sendToAbsentees, examId,
				courseId, sectionId, userId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(smsResponse).build();
	}

	@GET
	@Path("batch-notification-history/{delivery_mode}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getBatchNotificationHistory(@PathParam("delivery_mode") DeliveryMode deliveryMode,
												@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
												@QueryParam("offset") int offset, @QueryParam("limit") int limit) {

		final SearchResultWithPagination<NotificationBatchDetails<NotificationDetails>> notificationHistory = notificationManager
				.getBatchNotificationHistory(instituteId, academicSessionId,
						new HashSet<>(Arrays.asList(NotificationType.EXAM_OBTAINED_MARKS, NotificationType.EXAM_ABSENTEE)),
						deliveryMode, UserType.STUDENT, offset, limit);

		return Response.status(Response.Status.OK.getStatusCode()).entity(notificationHistory).build();
	}

	@GET
	@Path("individual-notification-history/{delivery_mode}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getIndividualNotificationHistory(@PathParam("delivery_mode") DeliveryMode deliveryMode,
													 @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
													 @QueryParam("offset") int offset, @QueryParam("limit") int limit) {

		final SearchResultWithPagination<NotificationDetails> notificationHistory = notificationManager
				.getIndividualNotificationHistory(instituteId, academicSessionId,
						new HashSet<>(Arrays.asList(NotificationType.EXAM_OBTAINED_MARKS, NotificationType.EXAM_ABSENTEE)),
						deliveryMode, UserType.STUDENT, offset, limit);

		return Response.status(Response.Status.OK.getStatusCode()).entity(notificationHistory).build();
	}

	@GET
	@Path("batch-notification-detail/{user_type}/{batch_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getBatchNotificationDetails(@PathParam("user_type") UserType userType,
												@PathParam("batch_id") UUID batchId, @QueryParam("institute_id") int instituteId) {

		final List<NotificationDetails> notificationDetails = notificationManager
				.getBatchNotificationDetails(instituteId, batchId, userType);
		return Response.status(Response.Status.OK.getStatusCode()).entity(notificationDetails).build();
	}

	@POST
	@Path("datesheet-syllabus")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response addDatesheet(@QueryParam("user_id") UUID userId, @QueryParam("institute_id") int instituteId,
								 DatesheetAndSyllabusPayload datesheetAndSyllabusPayload) {
		final boolean addedDatesheet = examinationManager.addDatesheet(userId, instituteId, datesheetAndSyllabusPayload);
		if (!addedDatesheet) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Unable to add datesheet"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(addedDatesheet).build();
	}

	@PUT
	@Path("datesheet-syllabus")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateDatesheet(@QueryParam("user_id") UUID userId, @QueryParam("institute_id") int instituteId,
									DatesheetAndSyllabusPayload datesheetAndSyllabusPayload) {
		final boolean updatedDatesheet = examinationManager.updateDatesheet(userId, instituteId, datesheetAndSyllabusPayload);
		if (!updatedDatesheet) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Unable to update datesheet"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(updatedDatesheet).build();
	}

	@DELETE
	@Path("datesheet-syllabus")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response deleteDatesheet(@QueryParam("user_id") UUID userId, @QueryParam("institute_id") int instituteId,
									@QueryParam("datesheet_id") UUID datesheetId) {
		final boolean deletedDatesheet = examinationManager.deleteDatesheet(userId, instituteId, datesheetId);
		if (!deletedDatesheet) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Unable to delete datesheet"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(deletedDatesheet).build();
	}

	@GET
	@Path("datesheet-syllabus/{institute_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getDatesheet(@PathParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academic_session_id,
								 @QueryParam("standard_id") UUID standardId) {
		final List<DatesheetMetadata> datesheetMetadataList = examinationManager.getDatesheet(instituteId, academic_session_id, standardId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(datesheetMetadataList).build();
	}

	@GET
	@Path("student-datesheet-syllabus/{institute_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentDatesheet(@PathParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academic_session_id,
										@QueryParam("student_id") UUID studentId) {
		final List<DatesheetMetadata> datesheetMetadataList = examinationManager.getStudentDatesheet(
				instituteId, academic_session_id, studentId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(datesheetMetadataList).build();
	}

	@GET
	@Path("datesheet-details/{institute_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getDatesheetDetailsByDatesheetId(@PathParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
													 @QueryParam("standard_id") UUID standardId, @QueryParam("datesheet_id") UUID datesheetId) {
		final DatesheetDetails datesheetDetails = examinationManager.getDatesheetDetailsByDatesheetId(instituteId,
				academicSessionId, standardId, datesheetId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(datesheetDetails).build();
	}

	@GET
	@Path("datesheet-details-by-exam/{institute_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getDatesheetDetailsByExamId(@PathParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
												@QueryParam("standard_id") UUID standardId, @QueryParam("exam_id") UUID examId) {
		final DatesheetDetails datesheetDetails = examinationManager.getDatesheetDetailsByExamId(instituteId,
				academicSessionId, standardId, examId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(datesheetDetails).build();
	}

	@DELETE
	@Path("student-marks")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response deleteStudentMarks(@QueryParam("institute_id") int instituteId,
									   @QueryParam("academic_session_id") int academicSessionId,
									   @QueryParam("admission_number") String admissionNumber,
									   @QueryParam("user_id") UUID userId) {
		Boolean deleted = examinationManager.deleteStudentAllMarksInSession(instituteId, academicSessionId, admissionNumber, userId);
		if (deleted == null) {
			return Response.status(Response.Status.OK.getStatusCode()).entity(Collections.singletonMap("status", "No marks available")).build();
		}
		if (!deleted) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to delete marks for student."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(Collections.singletonMap("status", "Marks deleted successfully")).build();
	}

	@DELETE
	@Path("clear-student-marks")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response deleteStudentMarksByStudentId(@QueryParam("institute_id") int instituteId,
												  @QueryParam("academic_session_id") int academicSessionId,
												  @QueryParam("student_id") UUID studentId,
												  @QueryParam("user_id") UUID userId) {
		Boolean deleted = examinationManager.deleteStudentAllMarksInSession(instituteId, academicSessionId, studentId, userId);
		if (deleted == null) {
			return Response.status(Response.Status.OK.getStatusCode()).entity(Collections.singletonMap("status", "No marks available")).build();
		}
		if (!deleted) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to delete marks for student."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(Collections.singletonMap("status", "Marks deleted successfully")).build();
	}

	@GET
	@Path("student-datesheet-details/{institute_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getSortedDatesheetDetailRow(@PathParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
												@QueryParam("standard_id") UUID standardId, @QueryParam("exam_id") UUID examId,
												@QueryParam("datesheet_id") UUID datesheetId) {
		final Map<Integer, List<DatesheetDetailRow>> datesheetDetailRowMap = examinationManager.getSortedDatesheetDetailRow(instituteId, academicSessionId,
				standardId, examId, datesheetId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(datesheetDetailRowMap).build();
	}

	@GET
	@Path("{standard_id}/pdf/{datesheet_id}")
	@Produces("application/pdf")
	public Response getTransactionInvoiceSummaryPDF(@PathParam("standard_id") UUID standardId,
													@PathParam("datesheet_id") UUID datesheetId,
													@QueryParam("academic_session_id") int academicSessionId,
													@QueryParam("institute_id") int instituteId) {

		final DocumentOutput documentOutput = datesheetHandler.generateDatesheet(instituteId, academicSessionId,
				standardId, datesheetId);
		if (documentOutput == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
					"Unable to generate datesheet pdf."));
		}

		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@PUT
	@Path("{exam_id}/exam-status/{status}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateExamCoursesStatus(@PathParam("status") ExamCoursePublishedStatus examCoursePublishedStatus,
											@PathParam("exam_id") UUID examId,
											@QueryParam("user_id") UUID userId, @QueryParam("institute_id") int instituteId) {
		final boolean updatesStatus = examinationManager.updateExamCoursesStatus(instituteId, userId, examId, examCoursePublishedStatus);
		if (!updatesStatus) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Unable to update datesheet"));
		}

		return Response.status(Response.Status.OK.getStatusCode()).entity(updatesStatus).build();
	}

	@GET
	@Path("{exam_id}/marks-status")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getCourseDimensionSectionMarksStatusDetails(@PathParam("exam_id") UUID examId, @QueryParam("institute_id") int instituteId,
																@QueryParam("academic_session_id") int academicSessionId,
																@QueryParam("user_id") UUID userId) {
		final List<CourseDimensionSectionMarksStatusDetails> courseDimensionSectionMarksStatusDetailsList = examinationManager
				.getCourseDimensionSectionMarksStatusDetails(instituteId, academicSessionId, examId, userId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(courseDimensionSectionMarksStatusDetailsList).build();
	}

	@PUT
	@Path("exam-marks-feed-status/{status}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateExamMarksFeedStatus(@PathParam("status") MarksFeedStatus marksFeedStatus, @QueryParam("exam_id") UUID examId,
											  @QueryParam("course_id") UUID courseId, @QueryParam("dimension_id") int dimensionId,
											  @QueryParam("section_id") Integer sectionId, @QueryParam("academic_session_id") int academicSessionId,
											  @QueryParam("user_id") UUID userId, @QueryParam("institute_id") int instituteId,
											  @QueryParam("standard_id") UUID standardId) {
		final boolean statusUpdated = examinationManager.updateExamMarksFeedStatus(instituteId, academicSessionId, userId, marksFeedStatus, examId, courseId, dimensionId, standardId, sectionId);
		if (!statusUpdated) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Unable to update exam marks feed status."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}


	// ---------------------------------------------Personality Traits--------------------------------------------------//
	@POST
	@Path("personality-traits")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response addPersonalityTraitsDetails(@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
												@QueryParam("user_id") UUID userId, List<PersonalityTraitsDetails> personalityTraitsDetailList) {
		final boolean addedPersonalityTraitsDetails = examinationManager.addPersonalityTraitsDetails(instituteId, academicSessionId, userId, personalityTraitsDetailList);
		if (!addedPersonalityTraitsDetails) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Could not add personality traits"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@PUT
	@Path("personality-traits/{institute_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updatePersonalityTraitsDetails(@PathParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
												   @QueryParam("user_id") UUID userId, List<PersonalityTraitsDetails> personalityTraitsDetailList) {
		final boolean updatedPersonalityTraitsDetails = examinationManager.updatePersonalityTraitsDetails(instituteId, academicSessionId, userId, personalityTraitsDetailList);
		if (!updatedPersonalityTraitsDetails) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Could not update personality traits"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@DELETE
	@Path("personality-trait")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response deletePersonalityTraitsDetails(@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
												   @QueryParam("user_id") UUID userId, @QueryParam("standard_id") UUID standardId,
												   @QueryParam("personality_traits_str") String personalityTraitsStr) {
		if (StringUtils.isBlank(personalityTraitsStr)) {
			return null;
		}
		String[] personalityTraitsStrArr = personalityTraitsStr.split(",");
		List<UUID> personalityTraitIdList = new ArrayList<>();
		for (String personalityTraitIdStr : personalityTraitsStrArr) {
			personalityTraitIdList.add(UUID.fromString(personalityTraitIdStr));
		}
		final boolean deletedStudentsSection = examinationManager.deletePersonalityTraitsDetails(instituteId, academicSessionId, userId, standardId,
				personalityTraitIdList);
		if (!deletedStudentsSection) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to delete personality traits."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@GET
	@Path("institute-personality-traits/{institute_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getInstitutePersonalityTraits(@PathParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {
		final List<StandardPersonalityTraitsDetails> personalityTraitsDetailsList = examinationManager.getInstitutePersonalityTraits(instituteId,
				academicSessionId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(personalityTraitsDetailsList).build();
	}

	@GET
	@Path("standard-personality-traits/{institute_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStandardPersonalityTraits(@PathParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
												 @QueryParam("standard_id") UUID standardId) {
		final List<PersonalityTraitsDetails> personalityTraitsDetailsList = examinationManager.getStandardPersonalityTraits(instituteId,
				academicSessionId, standardId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(personalityTraitsDetailsList).build();
	}

	@GET
	@Path("personality-traits/standards")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getPersonalityTraitsForStandards(@QueryParam("institute_id") int instituteId,
													@QueryParam("academic_session_id") int academicSessionId,
													@QueryParam("standard_ids") String standardIdsStr) {
		if (StringUtils.isBlank(standardIdsStr)) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Standard IDs cannot be empty."));
		}

		List<UUID> standardIds = new ArrayList<>();
		String[] standardIdArray = standardIdsStr.split(",");
		for (String standardIdStr : standardIdArray) {
			if (StringUtils.isNotBlank(standardIdStr.trim())) {
				standardIds.add(UUID.fromString(standardIdStr.trim()));
			}
		}

		final List<String> uniqueTraitNames = examinationManager.getPersonalityTraitsForStandards(instituteId, academicSessionId, standardIds);
		return Response.status(Response.Status.OK.getStatusCode()).entity(uniqueTraitNames).build();
	}

	@DELETE
	@Path("personality-traits/bulk")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response deletePersonalityTraitsBulk(@QueryParam("institute_id") int instituteId,
												@QueryParam("academic_session_id") int academicSessionId,
												@QueryParam("user_id") UUID userId,
												@QueryParam("skip_approval") boolean skipApproval,
												PersonalityTraitBulkDeletePayload payload) {
		final PersonalityTraitDeletionResponse deletionResponse = examinationManager.deletePersonalityTraitsWithValidation(
				instituteId, academicSessionId, userId, payload.getStandardIds(), payload.getTraitNames(), skipApproval);

		return Response.status(Response.Status.OK.getStatusCode()).entity(deletionResponse).build();
	}

	@GET
	@Path("standard/{standard_id}/personality-traits")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentPersonalityTraits(@QueryParam("institute_id") int instituteId, @PathParam("standard_id") UUID standardId,
												@QueryParam("section_id") String sectionIdStr, @QueryParam("academic_session_id") int academicSessionId,
												@QueryParam("report_type") String reportType, @QueryParam("student_id") UUID studentId,
												@QueryParam("student_sorting_parameters") StudentSortingParameters studentSortingParameters) {
		final Integer sectionId = getSectionId(sectionIdStr);
		final List<StudentPersonalityTraitsDetails> studentPersonalityTraitsDetailsList = examReportCardManager
				.getStudentPersonalityTraits(instituteId, standardId, sectionId, academicSessionId, reportType, studentId, studentSortingParameters);
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentPersonalityTraitsDetailsList).build();
	}

	@POST
	@Path("student-personality-traits")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateReportCardAttribute(List<StudentPersonalityTraitsPayload> studentPersonalityTraitsPayloadList,
											  @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
											  @QueryParam("user_id") UUID userId, @QueryParam("report_type") String reportType) {
		final boolean updated = examReportCardManager.updateStudentPersonalityTraitsDetails(instituteId,
				academicSessionId, reportType, studentPersonalityTraitsPayloadList, userId);
		if (!updated) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to add personality traits"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@GET
	@Path("student-exams-result/{standard_id}/field-data")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentExamResultDetails(@PathParam("standard_id") UUID standardId,
												@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
												@QueryParam("section_ids_str") String sectionIdSetStr, @QueryParam("exam_ids_str") String examIdSetStr,
												@QueryParam("additional_courses_str") String additionalCoursesStr) {
		Set<UUID> examIdSet = StringHelper.convertStringToSetUUID(examIdSetStr);
		Set<UUID> additionalCourseIdSet = StringHelper.convertStringToSetUUID(additionalCoursesStr);
		final StudentManagementFieldDetails studentManagementFieldDetails = examReportCardManager
				.getStudentExamResultDetails(instituteId, academicSessionId, standardId,
						 //Only supporting to update roll number and section from examination for now.
						 examIdSet, additionalCourseIdSet, sectionIdSetStr, "SECTION,ROLL_NUMBER");
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentManagementFieldDetails).build();
	}

	//	-----Student Report Card Status APIs-----
	@GET
	@Path("student-report-card-status/{institute_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentReportCardStatusDetails(@PathParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
													  @QueryParam("standard_id") UUID standardId, @QueryParam("section_ids_str") String sectionIdSetStr,
													  @QueryParam("report_card_id") UUID reportCardId, @QueryParam("student_id") UUID studentId) {
		Set<Integer> sectionIdSet = StringHelper.convertStringToSetInteger(sectionIdSetStr);
		final List<StudentReportCardStatusDetails> studentReportCardStatusDetailsList = examinationManager
				.getStudentReportCardStatusDetails(instituteId, academicSessionId, standardId, sectionIdSet, reportCardId, studentId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentReportCardStatusDetailsList).build();
	}

	@GET
	@Path("student-report-card-list/{institute_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentReportCardDetails(@PathParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
													   @QueryParam("student_id") UUID studentId) {
		
		final List<StudentReportCardDetails> studentReportCardDetailsList = hpcFormManager
				.getStudentReportCardDetails(instituteId, academicSessionId, studentId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentReportCardDetailsList).build();
	}

	@POST
	@Path("student-report-card-status")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateStudentReportCardStatusPayload(StudentReportCardStatusPayload studentReportCardStatusPayload,
											  @QueryParam("institute_id") int instituteId,
											  @QueryParam("user_id") UUID userId) {
		final boolean updated = examinationManager.updateStudentReportCardStatusPayload(instituteId, userId, studentReportCardStatusPayload);
		if (!updated) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to update student report card status"));
		}

		if(studentReportCardStatusPayload.getStudentExamDisplayDataStatus() == StudentExamDisplayDataStatus.PUBLISHED) {
			studentReportCardPublishPushNotificationHandler.sendStudentReportCardPublishPushNotificationsAsync(
					instituteId, studentReportCardStatusPayload.getReportCardId(), studentReportCardStatusPayload.getStudentIdSet());
		}

		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	//	-----Student Report Card Status APIs-----


	@GET
	@Path("report-card-types/{standard_id}/personality-traits")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getReportCardTypeAndPersonalityTraits(@PathParam("standard_id") UUID standardId, @QueryParam("institute_id") int instituteId,
							  @QueryParam("academic_session_id") int academicSessionId) {

		final StandardReportCardTypePersonalityTraits standardReportCardTypePersonalityTraits = examinationManager.getReportCardTypeAndPersonalityTraits(instituteId, academicSessionId, standardId);

		return Response.status(Response.Status.OK.getStatusCode()).entity(standardReportCardTypePersonalityTraits).build();
	}
}