package com.lernen.cloud.data.server.institute;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.api.onboarding.institute.setup.InstitutePayload;
import com.lernen.cloud.core.api.common.CounterType;
import com.lernen.cloud.core.api.common.FileData;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.*;
import com.lernen.cloud.core.api.student.DownloadDocumentWrapper;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.lib.institute.InstituteManagementManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.data.server.RestFormDataHandler;
import com.sun.jersey.multipart.FormDataBodyPart;
import com.sun.jersey.multipart.FormDataParam;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.util.*;

@Path("/2.0/institute")
public class InstitueEndPoint {

	private final InstituteManager instituteManager;
	private final InstituteManagementManager instituteManagementManager;

	public InstitueEndPoint(InstituteManager instituteManager,InstituteManagementManager instituteManagementManager) {
		this.instituteManager = instituteManager;
		this.instituteManagementManager = instituteManagementManager;
	}

	@GET
	@Produces(MediaType.APPLICATION_JSON)
	public Response getAllInstitute() {
		final List<Institute> institutes = instituteManager.getAllInstitute();
		if (institutes == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Could not get requested institutes"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(institutes).build();
	}

	@GET
	@Path("{institute_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getInstitute(@PathParam("institute_id") int instituteId) {
		final Institute institute = instituteManager.getInstitute(instituteId);
		if (institute == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Could not get requested institute"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(institute).build();
	}

	@PUT
	@Path("update-details/{institute_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateInstituteDetails(@PathParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId, InstitutePayload institutePayload) {
		final boolean updateInstitute = instituteManager.updateInstituteDetails(instituteId, userId, institutePayload);
		if (!updateInstitute) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to update institute details"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@POST
	@Path("/upload-documents")
	@Consumes(MediaType.MULTIPART_FORM_DATA)
	@Produces(MediaType.APPLICATION_JSON)
	public Response uploadInstituteDocuments(@QueryParam("institute_id") int instituteId, @FormDataParam("file") List<FormDataBodyPart> bodyParts,
											 @FormDataParam("documentType") InstituteDocumentType instituteDocumentType,
											 @FormDataParam("documentName") String documentName,
											 @QueryParam("user_id") UUID userId){
		final List<FileData> files = RestFormDataHandler.getFileDataFromFormDataBodyPart(bodyParts);
		if (CollectionUtils.isEmpty(files)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "No file attached for upload."));
		}
		documentName = getValidDocumentName(instituteDocumentType);
		final List<Document<InstituteDocumentType>> uploadedDocuments = instituteManager.uploadDocument(instituteId, instituteDocumentType, documentName, files.get(0), userId);
		if (CollectionUtils.isEmpty(uploadedDocuments)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Unable to upload file"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(uploadedDocuments).build();
	}

	public String getValidDocumentName(InstituteDocumentType instituteDocumentType) {
		return instituteDocumentType.getDocumentName().toLowerCase();
	}

	@GET
	@Path("/download-document/{document_id}")
	@Produces(MediaType.APPLICATION_OCTET_STREAM)
	public Response downloadStudentDocument(@QueryParam("institute_id") int instituteId,
											@PathParam("document_id") UUID documentId) {
		final DownloadDocumentWrapper<Document<InstituteDocumentType>> documentWrapper = instituteManager.downloadDocument(instituteId, documentId);
		if (documentWrapper == null || documentWrapper.getDocumentContent() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Unable to download file"));
		}

		String documentName = getValidDocumentName(documentWrapper.getDocumentDetails().getDocumentType());
		return Response.status(Response.Status.OK.getStatusCode())
				.header("Content-Disposition",
						"attachment;filename=" + documentName + "."
								+ documentWrapper.getDocumentDetails().getFileExtension())
				.entity(new ByteArrayInputStream(documentWrapper.getDocumentContent().toByteArray())).build();
	}

	@POST
	@Path("/delete-document/{institute_id}/{document_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response deleteStudentDocument(@PathParam("institute_id") int instituteId,
										  @PathParam("document_id") UUID documentId,
										  @QueryParam("user_id") UUID userId) {
		boolean updatedDocuments = instituteManager.deleteDocument(instituteId, documentId, userId);
		if (!updatedDocuments) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Unable to delete document"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(updatedDocuments).build();
	}

	@GET
	@Path("uniquecode/{institute_unique_code}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getInstituteByUniqueId(@PathParam("institute_unique_code") UUID instituteUniqueCode) {
		final Institute institute = instituteManager.getInstitute(instituteUniqueCode);
		if (institute == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS,
					"Could not get requested institute by unique code"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(institute).build();
	}

	@GET
	@Path("metadata/{institute_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getInstituteMetaData(@PathParam("institute_id") int instituteId) {
		final InstituteMetaData instituteMetaData = instituteManager.getInstituteMetaData(instituteId);
		if (instituteMetaData == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS,
					"Could not get requested institute meta data"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(instituteMetaData).build();
	}

	@PUT
	@Path("status/{institute_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateInstituteActiveStatus(@PathParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId, @QueryParam("is_active") boolean isActive, @QueryParam("skip_auth") boolean skipAuth) {
		final boolean updated = instituteManager.updateInstituteActiveStatus(instituteId, userId, isActive, skipAuth);
		if (!updated) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to update active status of an institute"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@POST
	@Path("academic-session")
	@Produces(MediaType.APPLICATION_JSON)
	public Response addAcademicSession(@QueryParam("cloneAcademicSessionId") int cloneAcademicSessionId, @QueryParam("skipCloning") boolean skipCloning,
									   AcademicSession academicSession) {
		final List<String> added = instituteManager.addInstituteSession(academicSession, cloneAcademicSessionId, skipCloning);
		return Response.status(Response.Status.OK.getStatusCode()).entity(added).build();
	}

	@GET
	@Path("/academic-session/{institute_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getAcademicYear(@PathParam("institute_id") int instituteId) {
		final List<AcademicSession> academicSessionList = instituteManager.getAcademicSessionList(instituteId);
		if (academicSessionList == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Could not get academic session."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(academicSessionList).build();
	}

	@POST
	@Path("standard")
	@Produces(MediaType.APPLICATION_JSON)
	public Response addInstituteStandard(Standard standardPayload) {
		final UUID standardId = instituteManager.addInstituteStandard(standardPayload);
		if (standardId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Could not add standards."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(standardId).build();
	}

	@PUT
	@Path("update-standard")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateInstituteStandard(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId, Map<String, String> newStandardNameMap) {
		if (!instituteManager.updateInstituteStandard(instituteId, userId, newStandardNameMap)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Unable to update standards names."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@POST
	@Path("counter-value")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response addUpdateCounterValue(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId, List<CounterData> counterDataList) {
		if (!instituteManager.addUpdateCounterValue(instituteId, userId, counterDataList)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Unable to add initial counter value."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	// ------------------------------------------------------------- Manage Sections -------------------------------------------------------------------------------------------------------------//
	@POST
	@Path("standard-sections")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response addStandardSections(@QueryParam("institute_id") int instituteId,@QueryParam("academic_session_id") int academicSessionId,@QueryParam("user_id") UUID userId, InstituteStandardSectionsPayload instituteStandardSectionsPayload){
		final boolean addedStandardSections = instituteManagementManager.addStandardSections(instituteId,academicSessionId,userId,instituteStandardSectionsPayload);
		if (!addedStandardSections) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Could not add standard sections"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@PUT
	@Path("standard-sections/{institute_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateStandardSections(@PathParam("institute_id") int instituteId,@QueryParam("user_id") UUID userId,InstituteStandardSectionsPayload instituteStandardSectionsPayload){
		final boolean updatedStandardSections = instituteManagementManager.updateStandardSections(instituteId,userId,instituteStandardSectionsPayload);
		if (!updatedStandardSections) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Could not update standard sections"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@DELETE
	@Path("standard-sections")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response deleteStandardSections(@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,@QueryParam("user_id") UUID userId,@QueryParam("standard_id") UUID standardId,@QueryParam("section_ids")
										String sectionIds,@QueryParam("student_assign_section_id") Integer studentAssignSectionId) {


		if(StringUtils.isBlank(sectionIds)){
			return null;
		}
		String[] sectionIdStr = sectionIds.split(",");
		List<Integer> sectionIdInt = new ArrayList<>();
		for(String sectionIdString: sectionIdStr){
			sectionIdInt.add(Integer.parseInt(sectionIdString));
		}

		final boolean deletedStudentsSection = instituteManagementManager.deleteStandardSections(instituteId, academicSessionId,userId,standardId, sectionIdInt,studentAssignSectionId);
		if (!deletedStudentsSection) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to delete standard sections."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@PUT
	@Path("standards")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response bulkStandardManagement(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId, List<StandardData> standardDataList) {
		final boolean processed = instituteManagementManager.bulkStandardManagement(instituteId, userId, standardDataList);
		if (!processed) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Could not process bulk standard management"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	// ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

	@GET
	@Path("standard-without-section/{institute_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getInstituteStandardWithoutSectionList(@PathParam("institute_id") int instituteId) {
		final List<Standard> standardPayloadList = instituteManager.getInstituteStandardList(instituteId);

		if (standardPayloadList == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Could not get standards."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(standardPayloadList).build();
	}

	@GET
	@Path("standard/{institute_id}/academic-session/{academic_session_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getInstituteStandardList(@PathParam("institute_id") int instituteId,
			@PathParam("academic_session_id") int academicSessionId) {
		final List<Standard> standardPayloadList = instituteManager.getInstituteStandardList(instituteId,
				academicSessionId);

		if (standardPayloadList == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Could not get standards."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(standardPayloadList).build();
	}

	@GET
	@Path("standard/{institute_id}/academic-session/{academic_session_id}/details")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getInstituteStandardDetails(@PathParam("institute_id") int instituteId,
			@PathParam("academic_session_id") int academicSessionId) {
		if (instituteId <= 0 || academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Invalid institute or academic session."));
		}
		final List<Standard> standardPayloadList = instituteManager.getInstituteStandardDetails(instituteId,
				academicSessionId);

		if (standardPayloadList == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Could not get standards details."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(standardPayloadList).build();
	}

	@GET
	@Path("counter/{institute_id}/{counter_type}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getCounter(@PathParam("institute_id") int instituteId,
			@PathParam("counter_type") String counterTypeStr) {
		final CounterType counterType = CounterType.valueOf(counterTypeStr);
		final CounterData counterData = instituteManager.getCounter(instituteId, counterType, false);
		if (counterData == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Could not get requested counter"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(counterData).build();
	}

	@GET
	@Path("counters/{institute_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getCounters(@PathParam("institute_id") int instituteId,
							   @QueryParam("counter_types") String counterTypesStr) {

		final List<CounterData> counterData = instituteManager.getCounters(instituteId, counterTypesStr);
		if (counterData == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Could not get requested counters"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(counterData).build();
	}
	
	@GET
	@Path("next-session/{institute_id}/{academic_session_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getNextSessionDetails(@PathParam("institute_id") int instituteId,
			@PathParam("academic_session_id") int academicSessionId) {
		final AcademicSession academicSession = instituteManager.getNextSessionDetails(instituteId, academicSessionId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(academicSession).build();
	}

	@POST
	@Path("institute-houses")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response addInstituteHouses(InstituteHouse instituteHouse, @QueryParam("institute_id") int instituteId,
									   @QueryParam("user_id") UUID createUserId) {
		final UUID houseCreated = instituteManager.addInstituteHouse(instituteHouse, createUserId);
		if (houseCreated == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Could not institute houses"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(houseCreated).build();
	}

	@GET
	@Path("institute-house/{house_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getInstituteHousesDetailsByHouseId(@QueryParam("institute_id") int instituteId,
														@PathParam("house_id") UUID instituteHouseId) {
		final InstituteHouse instituteHouse = instituteManager.getInstituteHousesDetailsByHouseId(instituteId, instituteHouseId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(instituteHouse).build();
	}

	@GET
	@Path("institute-houses")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getInstituteHousesList(@QueryParam("institute_id") int instituteId) {
		final List<InstituteHouse> instituteHouseList = instituteManager.getInstituteHouseList(instituteId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(instituteHouseList).build();
	}

	@PUT
	@Path("institute-house-details")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateInstituteHouses(InstituteHouse instituteHouse, @QueryParam("institute_id") int instituteId,
										  @QueryParam("user_id") UUID updateUserId) {
		final boolean houseUpdated = instituteManager.updateInstituteHouse(instituteHouse, updateUserId);
		if (!houseUpdated) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Could not update institute house details"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@DELETE
	@Path("institute-house-details/{house_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response deleteInstituteHouses(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
										@PathParam("house_id") UUID instituteHouseId) {
		final boolean deleteHouse = instituteManager.deleteInstituteHouse(instituteId, userId, instituteHouseId);
		if (!deleteHouse) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to delete house details."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@GET
	@Path("institute-houses-count")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getInstituteHousesWithCount(@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {
		final List<InstituteHousesWithCount> instituteHousesWithCountList = instituteManager.getInstituteHousesWithCount(instituteId, academicSessionId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(instituteHousesWithCountList).build();
	}

	//<-------------------Standard Class Teacher Flow------------------->//

	@GET
	@Path("standard-staff-details")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStandardWithStaffDetailsList(@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
												@QueryParam("staff_id") UUID staffId, @QueryParam("standard_id") UUID standardId) {
		final List<StandardWithStaffDetails> standardWithStaffDetailsList = instituteManagementManager.getStandardWithStaffDetailsList(
				instituteId, academicSessionId, staffId, standardId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(standardWithStaffDetailsList).build();
	}

	@PUT
	@Path("standard-staff-details/{staff_id}/{standard_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateStandardStaffAssignment(@PathParam("staff_id") UUID staffId, @PathParam("standard_id") UUID standardId,
												  @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
												  @QueryParam("section_id") Integer sectionId, @QueryParam("user_id") UUID userId) {
		final boolean updated = instituteManagementManager.addUpdateStandardStaffAssignment(instituteId, academicSessionId, staffId, standardId, sectionId, userId);
		if (!updated) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to update standard staff assignment details"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@DELETE
	@Path("standard-staff-details/{standard_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response deleteStandardStaffAssignment(@PathParam("standard_id") UUID standardId, @QueryParam("institute_id") int instituteId,
										  @QueryParam("academic_session_id") int academicSessionId, @QueryParam("section_id") Integer sectionId,
										  @QueryParam("user_id") UUID userId) {
		final boolean deleted = instituteManagementManager.deleteStandardStaffAssignment(instituteId, academicSessionId, standardId,
				sectionId == null || sectionId <= 0 ? null : new HashSet<>(Collections.singletonList(sectionId)), userId);
		if (!deleted) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to delete standard staff assignment details."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@GET
	@Path("standard-staff-details-map")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStandardWithStaffDetailsMap(@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
													@QueryParam("staff_id") UUID staffId, @QueryParam("standard_id") UUID standardId) {
			Map<UUID, Set<Integer>> classTeacherStandardSectionDetails = instituteManager.getStandardWithStaffDetailsList(
					instituteId, academicSessionId, staffId, null);
		return Response.status(Response.Status.OK.getStatusCode()).entity(classTeacherStandardSectionDetails).build();
	}

	//<-------------------Standard Class Teacher Flow------------------->//

	//<-------------------Standard Session Documents Flow------------------->//

	@POST
	@Path("/upload-standard-session-documents")
	@Consumes(MediaType.MULTIPART_FORM_DATA)
	@Produces(MediaType.APPLICATION_JSON)
	public Response uploadStandardSessionDocuments(@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
		   @QueryParam("standard_id") UUID standardId, @QueryParam("section_id") Integer sectionId, @FormDataParam("file") List<FormDataBodyPart> bodyParts,
			@FormDataParam("documentName") String documentName, @QueryParam("user_id") UUID userId){
		final List<FileData> files = RestFormDataHandler.getFileDataFromFormDataBodyPart(bodyParts);
		if (CollectionUtils.isEmpty(files)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "No file attached for upload."));
		}
		if(StringUtils.isBlank(documentName)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Document name cannot be empty!."));
		}
		final List<Document<StandardSessionDocumentType>> uploadedDocuments = instituteManagementManager.uploadStandardSessionDocuments(
				instituteId, academicSessionId, standardId, sectionId, documentName, files.get(0), userId);
		if (CollectionUtils.isEmpty(uploadedDocuments)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Unable to upload file"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(uploadedDocuments).build();
	}

	@GET
	@Path("/download-standard-session-document/{document_id}")
	@Produces(MediaType.APPLICATION_OCTET_STREAM)
	public Response downloadStandardSessionDocument(@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
													@QueryParam("standard_id") UUID standardId, @QueryParam("section_id") Integer sectionId,
													@PathParam("document_id") UUID documentId) {
		final DownloadDocumentWrapper<Document<StandardSessionDocumentType>> documentWrapper = instituteManagementManager.downloadStandardSessionDocument(
				instituteId, academicSessionId, standardId, sectionId, documentId);
		if (documentWrapper == null || documentWrapper.getDocumentContent() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Unable to download file"));
		}

		String documentName = documentWrapper.getDocumentDetails().getDocumentName();
		return Response.status(Response.Status.OK.getStatusCode())
				.header("Content-Disposition",
						"attachment;filename=" + documentName + "."
								+ documentWrapper.getDocumentDetails().getFileExtension())
				.entity(new ByteArrayInputStream(documentWrapper.getDocumentContent().toByteArray())).build();
	}

	@POST
	@Path("/delete-standard-session-document/{standard_id}/{document_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response deleteStandardSessionDocument(@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
												  @PathParam("standard_id") UUID standardId, @QueryParam("section_id") Integer sectionId,
												  @PathParam("document_id") UUID documentId,
										  @QueryParam("user_id") UUID userId) {
		List<Document<StandardSessionDocumentType>> standardSessionDocumentList = instituteManagementManager.deleteStandardSessionDocument(instituteId, academicSessionId, standardId, sectionId, documentId, userId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(standardSessionDocumentList).build();
	}

	//<-------------------Standard Session Documents Flow------------------->//

	//<-------------------Institute Bank Account Details Flow------------------->//

	@POST
	@Path("bank-account/{institute_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response addInstituteBankAccount(@PathParam("institute_id") int instituteId,
									   @QueryParam("user_id") UUID userId, InstituteBankAccountDetails instituteBankAccountDetailsPayload) {
		final UUID accountId = instituteManager.addInstituteBankAccount(instituteId, userId, instituteBankAccountDetailsPayload);
		if (accountId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Could not add bank account"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@GET
	@Path("bank-accounts")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getInstituteBankAccounts(@QueryParam("institute_id") int instituteId) {
		final List<InstituteBankAccountDetails> instituteBankAccountDetailsList = instituteManager.getInstituteBankAccounts(instituteId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(instituteBankAccountDetailsList).build();
	}

	@GET
	@Path("bank-account/{account_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getInstituteBankAccount(@PathParam("account_id") UUID accountId, @QueryParam("institute_id") int instituteId) {
		final InstituteBankAccountDetails instituteBankAccountDetails = instituteManager.getInstituteBankAccount(instituteId, accountId);
		if (instituteBankAccountDetails == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Could not get institute bank account"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(instituteBankAccountDetails).build();
	}

	@PUT
	@Path("bank-account-details/{account_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateInstituteBankAccountDetails(@PathParam("account_id") UUID accountId, @QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId, InstituteBankAccountDetails instituteBankAccountDetailsPayload) {
		final boolean updateInstituteBankAccountDetails = instituteManager.updateInstituteBankAccountDetails(instituteId, userId, instituteBankAccountDetailsPayload, accountId);
		if (!updateInstituteBankAccountDetails) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to update institute details"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@DELETE
	@Path("bank-account/{account_id}/inactive")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateActiveStatusOfBankAccount(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
										  @PathParam("account_id") UUID accountId) {
		final boolean deleteBankAccount = instituteManager.updateActiveStatusOfBankAccount(instituteId, userId, accountId);
		if (!deleteBankAccount) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to delete bank account."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}
	//<-------------------Institute Bank Account Details Flow------------------->//

}