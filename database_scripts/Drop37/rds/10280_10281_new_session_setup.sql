select * from academic_session where institute_id = 10280;
select * from standard_section_mapping where academic_session_id = 172;
select * from standards_metadata where academic_session_id = 172;
select * from examination_grades where academic_session_id = 172;

--select standard_id, 208, section_name from standard_section_mapping where academic_session_id = 172;
--insert into standard_section_mapping(standard_id, academic_session_id, section_name) select standard_id, 208, section_name from standard_section_mapping where academic_session_id = 172;
--
--select institute_id, 208, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 172;
--insert into standards_metadata select institute_id, 208, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 172;
--
--select institute_id, 208, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 172;
--insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name) select institute_id, 208, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 172;

select * from academic_session where institute_id = 10280;
select * from standard_section_mapping where academic_session_id = 208;
select * from standards_metadata where academic_session_id = 208;
select * from examination_grades where academic_session_id = 208;



select * from academic_session where institute_id = 10281;
select * from standard_section_mapping where academic_session_id = 173;
select * from standards_metadata where academic_session_id = 173;
select * from examination_grades where academic_session_id = 173;

--select standard_id, 209, section_name from standard_section_mapping where academic_session_id = 173;
--insert into standard_section_mapping(standard_id, academic_session_id, section_name) select standard_id, 209, section_name from standard_section_mapping where academic_session_id = 173;
--
--select institute_id, 209, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 173;
--insert into standards_metadata select institute_id, 209, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 173;
--
--select institute_id, 209, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 173;
--insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name) select institute_id, 209, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 173;

select * from academic_session where institute_id = 10281;
select * from standard_section_mapping where academic_session_id = 209;
select * from standards_metadata where academic_session_id = 209;
select * from examination_grades where academic_session_id = 209;
