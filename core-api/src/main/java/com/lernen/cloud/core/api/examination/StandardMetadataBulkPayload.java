package com.lernen.cloud.core.api.examination;

import java.util.List;

/**
 * Payload for bulk upsert operations on standards metadata
 * 
 * <AUTHOR>
 */
public class StandardMetadataBulkPayload {

    private List<StandardMetadataItem> standardMetadataItems;

    public StandardMetadataBulkPayload() {
    }

    public StandardMetadataBulkPayload(List<StandardMetadataItem> standardMetadataItems) {
        this.standardMetadataItems = standardMetadataItems;
    }

    public List<StandardMetadataItem> getStandardMetadataItems() {
        return standardMetadataItems;
    }

    public void setStandardMetadataItems(List<StandardMetadataItem> standardMetadataItems) {
        this.standardMetadataItems = standardMetadataItems;
    }

    @Override
    public String toString() {
        return "StandardMetadataBulkPayload{" +
                "standardMetadataItems=" + standardMetadataItems +
                '}';
    }
}
