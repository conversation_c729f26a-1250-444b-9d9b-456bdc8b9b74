package com.lernen.cloud.core.api.institute;

import java.util.UUID;

/**
 * Standard data with metadata information
 * 
 * <AUTHOR>
 */
public class StandardWithMetadata {

    private final UUID standardId;
    private final String standardName;
    private final Stream stream;
    private final int level;
    private final String displayName;
    private final boolean scholasticGradeEnabled;
    private final boolean coScholasticGradeEnabled;
    private final boolean roundOffMarks;

    public StandardWithMetadata(UUID standardId, String standardName, Stream stream, int level,
                               boolean scholasticGradeEnabled, boolean coScholasticGradeEnabled, 
                               boolean roundOffMarks) {
        this.standardId = standardId;
        this.standardName = standardName;
        this.stream = stream;
        this.level = level;
        this.displayName = standardName + " - " + stream.name();
        this.scholasticGradeEnabled = scholasticGradeEnabled;
        this.coScholasticGradeEnabled = coScholasticGradeEnabled;
        this.roundOffMarks = roundOffMarks;
    }

    public UUID getStandardId() {
        return standardId;
    }

    public String getStandardName() {
        return standardName;
    }

    public Stream getStream() {
        return stream;
    }

    public int getLevel() {
        return level;
    }

    public String getDisplayName() {
        return displayName;
    }

    public boolean isScholasticGradeEnabled() {
        return scholasticGradeEnabled;
    }

    public boolean isCoScholasticGradeEnabled() {
        return coScholasticGradeEnabled;
    }

    public boolean isRoundOffMarks() {
        return roundOffMarks;
    }

    @Override
    public String toString() {
        return "StandardWithMetadata{" +
                "standardId=" + standardId +
                ", standardName='" + standardName + '\'' +
                ", stream=" + stream +
                ", level=" + level +
                ", displayName='" + displayName + '\'' +
                ", scholasticGradeEnabled=" + scholasticGradeEnabled +
                ", coScholasticGradeEnabled=" + coScholasticGradeEnabled +
                ", roundOffMarks=" + roundOffMarks +
                '}';
    }
}
