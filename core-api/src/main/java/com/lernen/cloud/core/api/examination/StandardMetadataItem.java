package com.lernen.cloud.core.api.examination;

import java.util.UUID;

/**
 * Represents a single standard metadata item for bulk operations
 * 
 * <AUTHOR>
 */
public class StandardMetadataItem {

    private UUID standardId;
    private boolean coScholasticGradeEnabled;
    private boolean scholasticGradeEnabled;
    private boolean roundExamReportMarks;

    public StandardMetadataItem() {
    }

    public StandardMetadataItem(UUID standardId, boolean coScholasticGradeEnabled, 
                               boolean scholasticGradeEnabled, boolean roundExamReportMarks) {
        this.standardId = standardId;
        this.coScholasticGradeEnabled = coScholasticGradeEnabled;
        this.scholasticGradeEnabled = scholasticGradeEnabled;
        this.roundExamReportMarks = roundExamReportMarks;
    }

    public UUID getStandardId() {
        return standardId;
    }

    public void setStandardId(UUID standardId) {
        this.standardId = standardId;
    }

    public boolean isCoScholasticGradeEnabled() {
        return coScholasticGradeEnabled;
    }

    public void setCoScholasticGradeEnabled(boolean coScholasticGradeEnabled) {
        this.coScholasticGradeEnabled = coScholasticGradeEnabled;
    }

    public boolean isScholasticGradeEnabled() {
        return scholasticGradeEnabled;
    }

    public void setScholasticGradeEnabled(boolean scholasticGradeEnabled) {
        this.scholasticGradeEnabled = scholasticGradeEnabled;
    }

    public boolean isRoundExamReportMarks() {
        return roundExamReportMarks;
    }

    public void setRoundExamReportMarks(boolean roundExamReportMarks) {
        this.roundExamReportMarks = roundExamReportMarks;
    }

    @Override
    public String toString() {
        return "StandardMetadataItem{" +
                "standardId=" + standardId +
                ", coScholasticGradeEnabled=" + coScholasticGradeEnabled +
                ", scholasticGradeEnabled=" + scholasticGradeEnabled +
                ", roundExamReportMarks=" + roundExamReportMarks +
                '}';
    }
}
