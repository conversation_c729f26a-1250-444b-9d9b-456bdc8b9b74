package com.lernen.cloud.dao.tier.institute;

import com.embrate.cloud.core.api.cache.ICacheKey;
import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.api.institute.StandardMetadata;
import com.embrate.cloud.core.api.onboarding.institute.setup.InstitutePayload;
import com.embrate.cloud.core.api.onboarding.institute.setup.InstituteSetupInput;
import com.lernen.cloud.core.api.common.CounterType;
import com.lernen.cloud.core.api.common.DBLockMode;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.institute.*;
import com.lernen.cloud.core.api.organisation.InstituteConfigurationPayload;
import com.lernen.cloud.core.api.organisation.Organisation;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.utils.Pair;
import com.lernen.cloud.core.utils.cache.CacheFactory;
import com.lernen.cloud.core.utils.cache.ICache;
import com.lernen.cloud.core.utils.cache.ICacheLoader;
import com.lernen.cloud.core.utils.crypto.CryptoUtils;
import com.lernen.cloud.core.utils.exceptions.ExceptionHandling;
import com.lernen.cloud.dao.tier.institute.mappers.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;

import static com.lernen.cloud.core.utils.SharedConstants.GSON;

public class InstituteDao {
    private static final Logger logger = LogManager.getLogger(InstituteDao.class);

    private final JdbcTemplate jdbcTemplate;
    private final TransactionTemplate transactionTemplate;

    private final ICache<InstituteHouseCacheKey, List<InstituteHouse>> instituteHouseCache;

    private final ICache<InstituteCacheKey, Institute> instituteCache;
    private  String assetBaseUrl;

    private final InstituteRowMapper instituteRowMapper;

    private final OrganisationRowMapper organisationRowMapper;


	public InstituteDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate, CacheFactory cacheFactory, String assetBaseUrl, InstituteRowMapper instituteRowMapper, OrganisationRowMapper organisationRowMapper) {
        this.jdbcTemplate = jdbcTemplate;
        this.transactionTemplate = transactionTemplate;
        this.instituteHouseCache = cacheFactory.getInMemoryCache(new InstituteHouseCacheLoader());
        this.instituteCache = cacheFactory.getInMemoryCache(new InstituteCacheLoader());
        this.assetBaseUrl = assetBaseUrl;
        this.instituteRowMapper = instituteRowMapper;
        this.organisationRowMapper = organisationRowMapper;
	}

    /**
     * Local cache which stores the recently fetched information from DAO. If any
     * entries are added in this table server needs to be restarted.
     * <p>
     * TODO : Proper caching with invalidation is needed. This is temporary
     * arrangement to save dao calls
     * InstituteId, AcademicSessionId, AcademicSession
     */
    private static final Map<Integer, Map<Integer, AcademicSession>> ACADEMIC_SESSION_LOCAL_CACHE = new HashMap<>();
    private static final StandardSectionDetailsRowMapper  STANDARD_SECTION_DETAILS_ROW_MAPPER   =   new StandardSectionDetailsRowMapper();
    private static final CounterRowMapper COUNTER_ROW_MAPPER = new CounterRowMapper();
    private static final StandardMetadataRowMapper STANDARD_META_DATA_ROW_MAPPER = new StandardMetadataRowMapper();

    private static final AcademicSessionRowMapper ACADEMIC_SESSION_ROW_MAPPER = new AcademicSessionRowMapper();
    private static final StandardRowDetailsRowMapper STANDARD_ROW_DETAILS_ROW_MAPPER = new StandardRowDetailsRowMapper();
    private static final StandardRowDetailsWithCountRowMapper STANDARD_ROW_DETAILS_WITH_COUNT_ROW_MAPPER = new StandardRowDetailsWithCountRowMapper();
    private static final InstituteHousesRowMapper INSTITUTE_HOUSES_ROW_MAPPER = new InstituteHousesRowMapper();
    private static final InstituteHousesWithCountRowMapper INSTITUTE_HOUSES_WITH_COUNT_ROW_MAPPER = new InstituteHousesWithCountRowMapper();

    private static final StandardWithoutSessionRowMapper STANDARD_WITHOUT_SESSION_ROW_MAPPER = new StandardWithoutSessionRowMapper();

    private static final StandardRowDetailsWithStaffRowMapper STANDARD_ROW_DETAILS_WITH_STAFF_ROW_MAPPER = new StandardRowDetailsWithStaffRowMapper();
    private static final StandardSessionDataPayloadRowMapper STANDARD_SESSION_DATA_PAYLOAD_ROW_MAPPER = new StandardSessionDataPayloadRowMapper();
    private static final InstituteBankAccountRowMapper INSTITUTE_BANK_ACCOUNT_ROW_MAPPER = new InstituteBankAccountRowMapper();

    private static final String INSERT_COUNTER = " insert into counters (institute_id, counter_type, count, counter_prefix) values (?, ?, ?, ?) "
            + " on duplicate key update count = ?, counter_prefix = ? ";

    private static final String INSERT_ORGANISATION = "insert into organisation (organisation_id, name) values(?, ?)";

    private static final String INSERT_INSTITUTE = "insert into institute (institute_id, institute_name, address_line_1, address_line_2, " +
            " city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2, institute_unique_code," +
            " branch_name, organisation_id, metadata, is_active)" +
            " values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    private static final String GET_ALL_INSTITUTES = "select * from institute";
    private static final String GET_INSTITUTE_BY_INSTITUTE_ID = "select * from institute where institute_id = ?";
    private static final String GET_INSTITUTE_BY_INSTITUTE_UNIQUE_CODE = "select * from institute where institute_unique_code = ?";
    private static final String GET_ORGANIZATION_BY_INSTITUTE_ID = "select organisation.*, institute.* from institute left join organisation"
            + " on institute.organisation_id = organisation.organisation_id  where institute_id = ? or institute.organisation_id in "
            + "(select organisation_id from institute where institute_id = ?)";

    private static final String UPDATE_INSTITUTE_DOCUMENTS = "update institute set institute_documents = ? where institute_id = ?";

    private static final String UPDATE_INSTITUTE_DETAILS = "update institute set institute_name = ?, branch_name = ?, address_line_1 = ?, address_line_2 = ?, city = ?, state = ?, country = ?, zipcode = ?, landmark = ?, email = ?, phone_number = ?, letter_head_line1 = ?, letter_head_line2 = ?, metadata = ?  where institute_id = ? ";

    private static final String UPDATE_INSTITUTE_ACTIVE_STATUS = "update institute set is_active = ? where institute_id = ? ";

    private static final String GET_ORGANIZATION_BY_ID = "select organisation.*, institute.* from organisation"
            + " inner join institute on institute.organisation_id = organisation.organisation_id  "
            + " where organisation.organisation_id = ? ";

    private static final String ADD_INSTITUTE_SESSION = "INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month, display_name, payroll_start_year, payroll_end_year, payroll_start_month, payroll_end_month, payroll_display_name) " +
            " values(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    private static final String GET_SORTED_ACADEMIC_SESSION_BY_INSTITUTE_ID = "select * from academic_session where institute_id = ? order by start_year desc";
    private static final String ACADEMIC_SESSION_BY_INSTITUTE_IDS = "select * from academic_session where institute_id in %s order by start_year desc";
    private static final String GET_ACADEMIC_SESSION_BY_ACADEMIC_SESSION_ID = "select * from academic_session where academic_session_id = ?";
    private static final String ADD_INSTITUTE_STANDARD = "insert into standards(institute_id, standard_id, standard_name, stream, level)"
            + "values(?, ?, ?, ?, ?)";
    private static final String BULK_STANDARD_MANAGEMENT = "insert into standards(institute_id, standard_id, standard_name, stream, level)"
            + "values(?, ?, ?, ?, ?) on duplicate key update standard_name = values(standard_name), stream = values(stream), level = values(level)";
    private static final String ADD_STANDARD_SECTION_MAPPING = "insert into standard_section_mapping(standard_id, academic_session_id, section_name)"
            + "values(?, ?, ?)";
    private static final String GET_STANDARD_ROW_DETAILS_BY_INSTITUTE_ID = "select standards.*, standard_section_mapping.* from standards "
            + "left join standard_section_mapping on standards.standard_id = standard_section_mapping.standard_id "
            + "and standard_section_mapping.academic_session_id = ? where standards.institute_id = ? ";

    private static final String  UPDATE_STANDARD_SECTIONS = "update standard_section_mapping set section_name = ? where section_id = ? and standard_id = ? and academic_session_id = ? ";

    private static final String  DELETE_STANDARD_SECTIONS = "delete from standard_section_mapping where section_id = ? and standard_id = ? and academic_session_id = ? ";

    private static final String GET_STANDARD_DETAILS_WITH_STUDENT_COUNT_BY_INSTITUTE_ID = "select standards.*, standard_section_mapping.* , student_count from standards " +
            " left join standard_section_mapping on standards.standard_id = standard_section_mapping.standard_id and standard_section_mapping.academic_session_id = ? " +
            " left join (select standard_id , section_id , count(*) as student_count from student_academic_session_details " +
            " join students on student_academic_session_details.student_id = students.student_id where academic_session_id = ? and student_academic_session_details.session_status = 'ENROLLED' " +
            " group by standard_id, section_id) as student_counts on standards.standard_id = student_counts.standard_id and "
            + "student_counts.section_id <=> standard_section_mapping.section_id  where standards.institute_id = ?";

    private static final String GET_ALL_SESSION_STANDARD_DETAILS_WITHOUT_STUDENT_COUNT_BY_INSTITUTE_ID = "select * from standards " +
            " left join standard_section_mapping on standards.standard_id = standard_section_mapping.standard_id where standards.institute_id in ( %s ) ";

    private static final String GET_STANDARDS_BY_INSTITUTE_ID = " select * from standards where institute_id in ( %s ) ";
    private static final String GET_COUNTERS_BY_INSTITUTE_AND_TYPES = "select * from counters where institute_id = ? and counter_type in (%s)";
    private static final String GET_COUNT_BY_INSTITUTE_AND_TYPE = "select * from counters where institute_id = ? and counter_type = ? %s";
    private static final String UPDATE_COUNT_BY_INSTITUTE_AND_TYPE = "update counters set count = count + ? where institute_id = ? and counter_type = ?";

    private static final String UPDATE_PREFIX_BY_INSTITUTE_AND_TYPE = "update counters set counter_prefix = ? where institute_id = ? and counter_type = ?";

//	private static final String DECREMENT_COUNT_BY_INSTITUTE_AND_TYPE = "update counters set count = count - ? where institute_id = ? and counter_type = ?";

    private static final String CREATE_STANDARD_META_DATA = "INSERT INTO standards_metadata (institute_id, academic_session_id, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled) "
            + "VALUES (?, ?, ?, ?, ?);";

    private static final String GET_STANDARD_META_DATA = "select * from standards_metadata where institute_id = ? and academic_session_id = ? and standard_id = ? ";

    private static final String GET_NEXT_ACADEMIC_SESSION = " select * from academic_session "
            + " where institute_id = ? and end_year > (select end_year from academic_session where academic_session_id = ?)"
            + " order by end_year limit 1 ";

    private static final String GET_LATEST_ACADEMIC_SESSION = " select * from academic_session where institute_id = ? order by end_year desc limit 1 ";

    private static final String ADD_INSTITUTE_HOUSE_DETAILS = "insert into institute_houses (institute_id, "
            + " house_id, name, created_by) values(?, ?, ?, ?)";

    private static final String GET_INSTITUTE_HOUSE_DETAILS_BY_NAME = " select * from institute_houses "
            + " where institute_houses.institute_id = ? and institute_houses.name = ? ";

    private static final String GET_INSTITUTE_HOUSE_DETAILS_BY_ID = " select * from institute_houses "
            + " where institute_houses.institute_id = ? and institute_houses.house_id = ? ";

    private static final String GET_INSTITUTE_HOUSE_LIST = " select * from institute_houses "
            + " where institute_houses.institute_id = ? order by institute_houses.name asc ";

    private static final String UPDATE_INSTITUTE_HOUSE_DETAILS = " update institute_houses "
            + " set name = ?, updated_by = ? "
            + " where institute_houses.institute_id = ? and institute_houses.house_id = ? ";

    private static final String DELETE_INSTITUTE_HOUSE_BY_ID = "delete from institute_houses "
            + " where institute_houses.institute_id = ? and institute_houses.house_id = ? ";

    private static final String GET_SESSION_STUDENT_COUNT_BY_HOUSE = "select count(students.student_id) as student_house_count, institute_houses.house_id, institute_houses.* from institute_houses "
            + " inner join students on students.house_id = institute_houses.house_id "
            + " inner join student_academic_session_details on student_academic_session_details.student_id = students.student_id and student_academic_session_details.academic_session_id = ? "
            + " where institute_houses.institute_id = ? and students.final_status = 'ENROLLED' group by institute_houses.house_id ";

    private static final String GET_INSTITUTE_STUDENT_COUNT_BY_HOUSE = "select count(students.student_id) as student_house_count, institute_houses.house_id, institute_houses.* from institute_houses "
            + " inner join students on students.house_id = institute_houses.house_id "
            + " where institute_houses.institute_id = ? and students.final_status = 'ENROLLED' group by institute_houses.house_id ";

    private static final String GET_SECTION_DETAILS_BY_SECTION_NAME = "select * from standard_section_mapping where standard_id = ? and section_name = ? and academic_session_id = ?";

    private static final String GET_STANDARD_STAFF_DETAILS = "select * from standards "
            + " left join standard_section_mapping on standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = ? "
            + " left join standard_session_data on standard_session_data.institute_id = standards.institute_id and standard_session_data.academic_session_id = ? and standard_session_data.standard_id = standards.standard_id and (standard_session_data.section_id = standard_section_mapping.section_id or standard_session_data.section_id is null) "
            + " left join staff_details on staff_details.staff_id = standard_session_data.class_teacher_staff_id "
            + " where standards.institute_id = ? %s ";

    private static final String INSERT_STANDARD_SESSION_DATA = "insert into standard_session_data " +
            " (institute_id, academic_session_id, standard_id, section_id, class_teacher_staff_id) " +
            " values (?, ?, ?, ?, ?)";
    
    private static final String UPSERT_STANDARD_SESSION_DATA = "INSERT into standard_session_data "+
            " (institute_id, academic_session_id, standard_id, section_id, class_teacher_staff_id) " +
            " values (?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE class_teacher_staff_id = VALUES(class_teacher_staff_id)";

    private static final String UPDATE_STANDARD_SESSION_DATA = "update standard_session_data set class_teacher_staff_id = ? " +
            " where institute_id = ? and academic_session_id = ? and standard_id = ? %s ";

    private static final String DELETE_STANDARD_SESSION_DATA = "delete from standard_session_data "
            + " where institute_id = ? and academic_session_id = ? and standard_id = ? %s ";

    private static final String GET_STANDARD_SESSION_DATA = "select * from standard_session_data "
            + " where institute_id = ? and academic_session_id = ? %s %s %s ";

    private static final String UPDATE_STANDARD_SESSION_DOCUMENTS = "update standard_session_data set standard_session_documents = ? "
            + " where standard_session_data.institute_id = ? and standard_session_data.academic_session_id = ? and standard_session_data.standard_id = ? %s";

    private static final String UPDATE_STANDARD_NAME = " update standards set standard_name = ?, stream = ? where institute_id = ? and standard_id = ? ";

    private static final String ADD_INSTITUTE_BANK_ACCOUNT = " insert into institute_bank_account_details (institute_id, bank_account_id, account_holder_name, bank_name, branch_name, account_number, ifsc_code, account_type, status, is_primary, created_at) "
            + " values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";

    private static final String GET_INSTITUTE_BANK_ACCOUNTS = " select * from institute_bank_account_details "
            + " where institute_id = ? ";

    private static final String ACCOUNT_ID_CLAUSE = " and bank_account_id = ? ";

    private static final String ACCOUNT_IDS_CLAUSE = " and bank_account_id in ( %s ) ";

    private static final String UPDATE_INSTITUTE_BANK_ACCOUNT_DETAILS = " update institute_bank_account_details set account_holder_name = ?, bank_name = ?, branch_name = ?, account_number = ?, ifsc_code = ?, account_type = ?, is_primary = ?, status = ?, updated_at = ? where institute_id = ? and bank_account_id = ? ";

    private static final String UPDATE_INSTITUTE_BANK_PRIMARY_STATUS = " update institute_bank_account_details set is_primary = ?, updated_at = ? where institute_id = ? and bank_account_id = ? ";

    private static final String UPDATE_ACTIVE_STATUS_INSTITUTE_BANK_PRIMARY_STATUS = " update institute_bank_account_details set status = ?, updated_at = ? where institute_id = ? and bank_account_id = ? ";

    public UUID addOrganisation(InstituteConfigurationPayload instituteConfigurationPayload) {
        if (instituteConfigurationPayload == null) {
            return null;
        }

        try {
            return transactionTemplate.execute(new TransactionCallback<UUID>() {
                @Override
                public UUID doInTransaction(TransactionStatus status) {
                    UUID organisationId = UUID.randomUUID();
                    int rows = jdbcTemplate.update(INSERT_ORGANISATION, organisationId.toString(), instituteConfigurationPayload.getOrganisationName().trim());
                    if (rows != 1) {
                        logger.error("Error while adding organisation for payload {}", instituteConfigurationPayload);
                        throw new EmbrateRunTimeException("Error while adding organisation");
                    }

                    for (InstituteSetupInput instituteSetupInput : instituteConfigurationPayload.getInstituteSetupInputList()) {
                        Integer instituteId = addInstitute(InstitutePayload.getInstituteCreationPayload(instituteSetupInput.getInstitute()), organisationId);
                        if (instituteId == null) {
                            logger.error("Error while adding institute for payload {}", instituteConfigurationPayload);
                            throw new EmbrateRunTimeException("Error while adding institute");
                        }
                    }
                    return organisationId;
                }
            });
        } catch (final Exception e) {
            logger.error("Error while creating organisation for payload id {}", instituteConfigurationPayload, e);
        }
        return null;
    }

    public Integer addInstitute(Institute institute, UUID organisationId) {
        if (institute == null) {
            return null;
        }
        try {
            UUID instituteUniqueCode = UUID.randomUUID();

            final String instituteMetadataVariablesMap = CollectionUtils.isEmpty(institute.getInstituteMetadataVariablesMap()) ? null
                    : GSON.toJson(institute.getInstituteMetadataVariablesMap());

            int row = jdbcTemplate.update(INSERT_INSTITUTE, institute.getInstituteId(), institute.getInstituteName().trim(),
                    StringUtils.isBlank(institute.getAddressLine1()) ? null : institute.getAddressLine1().trim(),
                    StringUtils.isBlank(institute.getAddressLine2()) ? null : institute.getAddressLine2().trim(),
                    StringUtils.isBlank(institute.getCity()) ? null : institute.getCity().trim(),
                    StringUtils.isBlank(institute.getState()) ? null : institute.getState().trim(),
                    StringUtils.isBlank(institute.getCountry()) ? null : institute.getCountry().trim(),
                    StringUtils.isBlank(institute.getZipcode()) ? null : institute.getZipcode().trim(),
                    StringUtils.isBlank(institute.getLandmark()) ? null : institute.getLandmark().trim(),
                    StringUtils.isBlank(institute.getLogoUrl()) ? null : institute.getLogoUrl().trim(),
                    StringUtils.isBlank(institute.getEmail()) ? null : institute.getEmail().trim(),
                    StringUtils.isBlank(institute.getPhoneNumber()) ? null : institute.getPhoneNumber().trim(),
                    StringUtils.isBlank(institute.getLetterHeadLine1()) ? null : institute.getLetterHeadLine1().trim(),
                    StringUtils.isBlank(institute.getLetterHeadLine2()) ? null : institute.getLetterHeadLine2().trim(),
                    instituteUniqueCode.toString(), institute.getBranchName() == null ? null : institute.getBranchName().trim(),
                    organisationId == null ? null : organisationId.toString(), instituteMetadataVariablesMap, true);
            if (row == 1) {
                return institute.getInstituteId();
            }
            return null;
        } catch (final Exception e) {
            logger.error("Error while creating institute for payload id {}", institute, e);
        } finally {
            invalidateInstituteCache(institute.getInstituteId());
        }

        return null;
    }

    public boolean addCounters(int instituteId, List<CounterData> counterDataList) {
        try {
            List<Object[]> args = new ArrayList<>();
            for (CounterData counterData : counterDataList) {
                // Counter prefix cannot be null
                args.add(new Object[]{instituteId, counterData.getCounterType().name(), counterData.getCount(),
                        StringUtils.isBlank(counterData.getCounterPrefix()) ? "" : counterData.getCounterPrefix().trim()
                        , counterData.getCount(), StringUtils.isBlank(counterData.getCounterPrefix()) ? "" : counterData.getCounterPrefix().trim()});
            }
            return jdbcTemplate.batchUpdate(INSERT_COUNTER, args).length == counterDataList.size();
        } catch (final Exception e) {
            logger.error("Error while creating counter for institute {}, payload {}", instituteId, counterDataList, e);
        }
        return false;
    }

    public List<Institute> getAllInstitute() {
        final Object[] args = {};
        try {
            return jdbcTemplate.query(GET_ALL_INSTITUTES, args, instituteRowMapper);
        } catch (final Exception e) {
            logger.error("Error while getting all institutes", e);
        }

        return null;
    }

    public List<Institute> getInstitutesByOrganisationId(UUID organisationId) {
        final Object[] args = {organisationId.toString()};
        try {
            StringBuilder queryBuilder = new StringBuilder();
            queryBuilder.append(GET_ALL_INSTITUTES);
            queryBuilder.append(" where organisation_id = ? ");
            return jdbcTemplate.query(queryBuilder.toString(), args, instituteRowMapper);
        } catch (final Exception e) {
            logger.error("Error while getting all institutes by organisation id {}", organisationId, e);
        }

        return null;
    }
    private Institute getInstituteListUnCached(int instituteId){
        if (instituteId <= 0) {
            return null;
        }
        final Object[] args = {instituteId};
        try {
            return jdbcTemplate.queryForObject(GET_INSTITUTE_BY_INSTITUTE_ID, args, instituteRowMapper);
        } catch (final Exception e) {
            logger.error("Error while getting institute by id {}", instituteId, e);
        }

        return null;
    }
    public Institute getInstitute(int instituteId) {
        return instituteCache.get(new InstituteCacheKey(instituteId));
    }

    public Institute getInstitute(UUID instituteUniqueCode) {
        if (instituteUniqueCode == null) {
            return null;
        }
        final Object[] args = {instituteUniqueCode.toString()};
        try {
            return jdbcTemplate.queryForObject(GET_INSTITUTE_BY_INSTITUTE_UNIQUE_CODE, args, instituteRowMapper);
        } catch (final Exception e) {
            logger.error("Error while getting institute by unique code {}", instituteUniqueCode, e);
        }

        return null;
    }

    public boolean updateInstitute( int instituteId, UUID userId, InstitutePayload institutePayload){
        try{
                final List<Object> args = new ArrayList<>();
                args.add(institutePayload.getInstituteName());
                args.add(institutePayload.getBranchName());
                args.add(institutePayload.getAddressLine1());
                args.add(institutePayload.getAddressLine2());
                args.add(institutePayload.getCity());
                args.add(institutePayload.getState());
                args.add(institutePayload.getCountry());
                args.add(institutePayload.getZipcode());
                args.add(institutePayload.getLandmark());
                args.add(CryptoUtils.encrypt(institutePayload.getEmail()));
                args.add(CryptoUtils.encrypt(institutePayload.getPhoneNumber()));
                args.add(institutePayload.getLetterHeadLine1());
                args.add(institutePayload.getLetterHeadLine2());
                args.add(CollectionUtils.isEmpty(institutePayload.getInstituteMetadataVariablesMap()) ? null :
                       GSON.toJson(institutePayload.getInstituteMetadataVariablesMap()));
                args.add(instituteId);

            return jdbcTemplate.update(UPDATE_INSTITUTE_DETAILS, args.toArray()) == 1 ;
        }   catch (final Exception e){
            logger.error("Unable to update institute", e);
        } finally {
            invalidateInstituteCache(instituteId);
        }
        return false;
    }

    public boolean updateInstituteActiveStatus( int instituteId, boolean isActive){
        try{
            final List<Object> args = new ArrayList<>();
            args.add(isActive);
            args.add(instituteId);

            return jdbcTemplate.update(UPDATE_INSTITUTE_ACTIVE_STATUS, args.toArray()) == 1 ;
        }   catch (final Exception e){
            logger.error("Unable to update institute active status", e);
        } finally {
            invalidateInstituteCache(instituteId);
        }
        return false;
    }

    public boolean updateDocuments(int instituteId, List<Document<InstituteDocumentType>> instituteDocuments){
        try {
            final Object[] args = { GSON.toJson(instituteDocuments), instituteId};
            return jdbcTemplate.update(UPDATE_INSTITUTE_DOCUMENTS, args) == 1;
        } catch (final DataAccessException dataAccessException) {
            ExceptionHandling.HandleException(dataAccessException, null, null);
            logger.error("Error while updating institute documents {}", instituteId);
        } catch (final Exception e) {
            logger.error("Exception while updating institute documents {}", instituteId, e);
        }
//        we are invalidating our current institute cache as we are updating our institute documents
        finally {
            invalidateInstituteCache(instituteId);
        }
        return false;
    }

    public Organisation getOrganization(int instituteId) {
        if (instituteId <= 0) {
            return null;
        }
        final Object[] args = {instituteId, instituteId};
        try {
            return OrganisationRowMapper.getOrganisationView(
                    jdbcTemplate.query(GET_ORGANIZATION_BY_INSTITUTE_ID, args, organisationRowMapper));
        } catch (final Exception e) {
            logger.error("Error while getting organisation by institute  id {}", instituteId, e);
        }

        return null;
    }

    public Organisation getOrganizationById(UUID organizationId) {
        if (organizationId == null) {
            return null;
        }
        final Object[] args = {organizationId.toString()};
        try {
            return OrganisationRowMapper.getOrganisationView(
                    jdbcTemplate.query(GET_ORGANIZATION_BY_ID, args, organisationRowMapper));
        } catch (final Exception e) {
            logger.error("Error while getting organisation by organization  id {}", organizationId, e);
        }

        return null;
    }

    public Map<Integer, List<AcademicSession>> getInstituteAcademicSessionMap(List<Integer> instituteIdList) {
        if (CollectionUtils.isEmpty(instituteIdList)) {
            return null;
        }
        Map<Integer, List<AcademicSession>> instituteAcademicSessionMap = new HashMap<>();
        for (Integer instituteId : instituteIdList) {
            List<AcademicSession> academicSessionList = getAcademicSessionListCache(instituteId);
            if (CollectionUtils.isEmpty(academicSessionList)) {
                academicSessionList = getAcademicSessionList(instituteId);
            }
            instituteAcademicSessionMap.put(instituteId, academicSessionList);
        }
        return instituteAcademicSessionMap;
    }

    public UUID addInstituteStandard(Standard standardPayload) {
        try {
            final UUID standardId = transactionTemplate.execute(new TransactionCallback<UUID>() {

                @Override
                public UUID doInTransaction(TransactionStatus status) {
                    final UUID instituteStandardId = addInstituteStandards(standardPayload);
                    if (instituteStandardId == null) {
                        throw new RuntimeException("Transaction could not update all entities in desired way");
                    }
                    return instituteStandardId;
                }
            });
            return standardId;
        } catch (final Exception e) {
            logger.error("Unable to execute transaction", e);
        }
        return null;
    }

    public boolean updateInstituteStandard(int instituteId, List<Pair<UUID, Pair<String, String>>> standardNamePairs) {
        try {
            final List<Object[]> argsList = new ArrayList<>();
            for (final Pair<UUID, Pair<String, String>> standardNamePair : standardNamePairs) {
                final List<Object> args = new ArrayList<>();
                args.add(standardNamePair.getSecond().getFirst());
                args.add(standardNamePair.getSecond().getSecond());
                args.add(instituteId);
                args.add(standardNamePair.getFirst().toString());
                argsList.add(args.toArray());
                logger.info("Executing update with params: instituteId={}, standardID={}, newName={}, newStream={}",
                        instituteId, standardNamePair.getFirst().toString(),
                        standardNamePair.getSecond().getFirst(), standardNamePair.getSecond().getSecond());

            }
            final int[] rows = jdbcTemplate.batchUpdate(UPDATE_STANDARD_NAME, argsList);
            if (rows.length != standardNamePairs.size()) {
                throw new RuntimeException("Unable to add bulk standard's section.");
            }
            for (int row : rows) {
                if (row == 0) {
                    throw new RuntimeException("Unable to add bulk standard's section.");
                }
            }
            return true;
        } catch (final Exception e) {
            logger.error("Error while updating the standard for instituteId {}", instituteId, e);
        }
        return false;
    }

    protected UUID addInstituteStandards(Standard standardPayload) {
        final UUID instituteStandardId = UUID.randomUUID();
        final boolean success = true;

        final int row = jdbcTemplate.update(ADD_INSTITUTE_STANDARD, standardPayload.getInstituteId(),
                instituteStandardId.toString(), standardPayload.getStandardName(), standardPayload.getStream().name(),
                standardPayload.getLevel());

        if (row == 1) {
            if (!CollectionUtils.isEmpty(standardPayload.getStandardSectionList())) {
                boolean result = addStandardSectionMapping(instituteStandardId, standardPayload.getAcademicSessionId(),
                        standardPayload.getStandardSectionList());
            }
        } else {
            throw new RuntimeException("Unable to add standards.");
        }
        return success ? instituteStandardId : null;
    }

    public boolean addStandardSectionMapping(UUID standardId, int academicSessionId,
                                           List<StandardSections> standardSectionList) {

        try {
            final List<Object[]> argsList = new ArrayList<>();
            for (final StandardSections standardSection : standardSectionList) {
                final List<Object> args = new ArrayList<>();
                args.add(standardId.toString());
                args.add(academicSessionId);
                args.add(standardSection.getSectionName());
                argsList.add(args.toArray());
            }
            final int[] rows = jdbcTemplate.batchUpdate(ADD_STANDARD_SECTION_MAPPING, argsList);
            if (rows.length != standardSectionList.size()) {
                throw new RuntimeException("Unable to add bulk standard's section.");
            }
            return true;
        }
        catch (final Exception e){
            logger.error("Error while adding the standard section for academic session {}", academicSessionId, e);
        }
        return false;
    }
    public boolean addInstituteSession(AcademicSession academicSession) {
        try {
            int rows = jdbcTemplate.update(ADD_INSTITUTE_SESSION, academicSession.getInstituteId(),
                    academicSession.getStartYear(), academicSession.getEndYear(), academicSession.getStartMonth().getValue(),
                    academicSession.getEndMonth().getValue(), academicSession.getDisplayName(),
                    academicSession.getPayrollStartYear(), academicSession.getPayrollEndYear(), academicSession.getPayrollStartMonth().getValue(),
                    academicSession.getPayrollEndMonth().getValue(), academicSession.getPayrollDisplayName());
            /**
             * Remove from caching
             */
            invalidateCache(academicSession.getInstituteId());
            return rows == 1;
        } catch (Exception e) {
            logger.error("Error while adding session for {}", academicSession, e);
        }
        return false;
    }


    /**
     * NULL academicSessionId will fetch only standards without any section mapping
     *
     * @param instituteId
     * @param academicSessionId
     * @return
     */
    public List<Standard> getInstituteStandardList(int instituteId, Integer academicSessionId) {
        try {
            final Object[] args = {academicSessionId == null ? 0 : academicSessionId, instituteId};
            return StandardRowDetailsRowMapper.getStandardResponseList(jdbcTemplate
                    .query(GET_STANDARD_ROW_DETAILS_BY_INSTITUTE_ID, args, STANDARD_ROW_DETAILS_ROW_MAPPER));
        } catch (final DataAccessException dataAccessException) {
            logger.warn("No meta data exists for institute {}", instituteId);
        } catch (final Exception e) {
            logger.error("Error occured while getting standard details for institute {}", instituteId, e);
        }

        return null;
    }

    public StandardSections getSectionDetailsByName(String sectionName,UUID standardId,int academicSessionId){
        try{
            final Object[] args = {standardId.toString(),sectionName,academicSessionId};
            return jdbcTemplate.queryForObject(GET_SECTION_DETAILS_BY_SECTION_NAME,args,STANDARD_SECTION_DETAILS_ROW_MAPPER);
        }
        catch(final Exception e){
            logger.error("Unable to get the section Id", e);
        }
        return null;
    }

    public boolean updateStandardSections(int instituteId,int academicSessionId,InstituteStandardSectionsPayload instituteStandardSectionsPayload){
        try{

            final List<Object[]> batchUpdateArgs = new ArrayList<>();
            int count = 0;
            for(StandardSections standardSections  :instituteStandardSectionsPayload.getStandardSectionsList()){
                final List<Object> args = new ArrayList<>();
                args.add(standardSections.getSectionName());
                args.add(standardSections.getSectionId());
                args.add(instituteStandardSectionsPayload.getStandardId().toString());
                args.add(academicSessionId);
                count++;
                batchUpdateArgs.add(args.toArray());
            }
            final int[] rows = jdbcTemplate.batchUpdate(UPDATE_STANDARD_SECTIONS,batchUpdateArgs);
            return rows.length == count;
        }
        catch (final Exception e){
            logger.error("Unable to update bulk students section details for institute {}", instituteId, e);
        }
        return false;
    }

    public boolean deleteStandardSections(int instituteId,int  academicSessionId,List<Integer> sectionIdInt,UUID standardId){
        try {

            final List<Object[]> batchDeleteArgs = new ArrayList<>();
            int count = 0;
            for (Integer sectionId : sectionIdInt) {
                final List<Object> args = new ArrayList<>();
                args.add(sectionId);
                args.add(standardId.toString());
                args.add(academicSessionId);
                count++;
                batchDeleteArgs.add(args.toArray());
            }
            final int[] rows = jdbcTemplate.batchUpdate(DELETE_STANDARD_SECTIONS, batchDeleteArgs);
            return rows.length == count;
        }
        catch (final Exception e){
            logger.error("Unable to delete bulk students section details for institute {}", instituteId, e);
        }
        return false;
    }

    public boolean bulkStandardManagement(int instituteId, List<StandardData> standardDataList) {
        try {
            final List<Object[]> batchArgs = new ArrayList<>();
            int count = 0;
            for (StandardData standardData : standardDataList) {
                final List<Object> args = new ArrayList<>();
                args.add(instituteId);
                args.add(standardData.getStandardId() != null ? standardData.getStandardId().toString() : UUID.randomUUID().toString());
                args.add(standardData.getStandardName());
                args.add(standardData.getStream().name());
                args.add(standardData.getLevel());
                count++;
                batchArgs.add(args.toArray());
            }
            final int[] rows = jdbcTemplate.batchUpdate(BULK_STANDARD_MANAGEMENT, batchArgs);
            return rows.length == count;
        } catch (final Exception e) {
            logger.error("Unable to process bulk standard management for institute {}", instituteId, e);
        }
        return false;
    }

    public List<Standard> getInstituteStandardDetails(int instituteId, int academicSessionId) {
        try {
            final Object[] args = {academicSessionId, academicSessionId, instituteId};
            return StandardRowDetailsRowMapper
                    .getStandardResponseList(jdbcTemplate.query(GET_STANDARD_DETAILS_WITH_STUDENT_COUNT_BY_INSTITUTE_ID,
                            args, STANDARD_ROW_DETAILS_WITH_COUNT_ROW_MAPPER));
        } catch (final DataAccessException dataAccessException) {
            logger.warn("No meta data exists for institute {}, session {}", instituteId, academicSessionId);
        } catch (final Exception e) {
            logger.error("Error occured while getting standard details for institute {}, session {}", instituteId,
                    academicSessionId, e);
        }

        return null;
    }

    public List<StandardRowDetails> getAllSessionStandardDetailsWithoutStudentCount(List<Integer> instituteIdList) {
        try {
            final List<Object> args = new ArrayList<Object>();
            StringBuilder query = new StringBuilder();
            String delimiter = "";
            for (Integer instituteId : instituteIdList) {
                args.add(instituteId);
                query.append(delimiter).append(" ?");
                delimiter = ",";
            }
            return jdbcTemplate.query(String.format(GET_ALL_SESSION_STANDARD_DETAILS_WITHOUT_STUDENT_COUNT_BY_INSTITUTE_ID, query),
                            args.toArray(), STANDARD_ROW_DETAILS_ROW_MAPPER);
        } catch (final DataAccessException dataAccessException) {
            logger.warn("No meta data exists for institute {}", instituteIdList);
        } catch (final Exception e) {
            logger.error("Error occured while getting standard details for institute {}", instituteIdList, e);
        }

        return null;
    }

    public List<Standard> getStandardDetailsWithoutSession(List<Integer> instituteIdList) {
        try {
            final List<Object> args = new ArrayList<Object>();
            StringBuilder query = new StringBuilder();
            String delimiter = "";
            for (Integer instituteId : instituteIdList) {
                args.add(instituteId);
                query.append(delimiter).append(" ?");
                delimiter = ",";
            }
            return jdbcTemplate.query(String.format(GET_STANDARDS_BY_INSTITUTE_ID, query),
                    args.toArray(), STANDARD_WITHOUT_SESSION_ROW_MAPPER);
        } catch (final DataAccessException dataAccessException) {
            logger.warn("No meta data exists for institute {}", instituteIdList);
        } catch (final Exception e) {
            logger.error("Error occured while getting standard details for institute {}", instituteIdList, e);
        }

        return null;
    }


    public CounterData getCounter(int instituteId, CounterType counterType, boolean forUpdate) {
        if ((instituteId <= 0) || (counterType == null)) {
            return null;
        }
        final String forUpdateClause = forUpdate ? DBLockMode.FOR_UPDATE.getCommand() : "";
        final Object[] args = {instituteId, counterType.name()};
        try {
            return jdbcTemplate.queryForObject(String.format(GET_COUNT_BY_INSTITUTE_AND_TYPE, forUpdateClause), args,
                    COUNTER_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Error while fetching the counter for {}, {}, {}", instituteId, counterType, forUpdate, e);
        }

        return null;
    }

    public List<CounterData> getCounters(int instituteId, Set<CounterType> counterTypeSet) {
        if ((instituteId <= 0) || CollectionUtils.isEmpty(counterTypeSet)) {
            return null;
        }
        final String forUpdateClause = "";
        final List<Object> args = new ArrayList<>();
        args.add(instituteId);

        StringBuilder sb = new StringBuilder();
        String delimiter = "";
        for (CounterType counterType : counterTypeSet) {
            args.add(counterType.name());
            sb.append(delimiter).append(" ?");
            delimiter = ",";
        }

        try {
            return jdbcTemplate.query(String.format(GET_COUNTERS_BY_INSTITUTE_AND_TYPES, sb.toString()), args.toArray(),
                    COUNTER_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Error while fetching the counters for {}, {}", instituteId, counterTypeSet, e);
        }

        return null;
    }


    public boolean incrementCounter(int instituteId, CounterType counterType) {
        return updateCounter(instituteId, counterType, 1);
    }

    public boolean updateCounter(int instituteId, CounterType counterType, Integer count) {
        if ((instituteId <= 0) || (counterType == null)) {
            return false;
        }

        final Object[] args = {count, instituteId, counterType.name()};
        try {
            return jdbcTemplate.update(UPDATE_COUNT_BY_INSTITUTE_AND_TYPE, args) == 1;
        } catch (final Exception e) {
            logger.error("Error while updating counter for instituteId {}, counterType {}, count {}", instituteId,
                    counterType, count, e);
        }

        return false;
    }

    public boolean updateCounterPrefix(int instituteId, CounterType counterType, String counterPrefix) {
        if ((instituteId <= 0) || (counterType == null)) {
            return false;
        }

        final Object[] args = {StringUtils.isBlank(counterPrefix) ? "" : counterPrefix.trim(), instituteId, counterType.name()};
        try {
            return jdbcTemplate.update(UPDATE_PREFIX_BY_INSTITUTE_AND_TYPE, args) == 1;
        } catch (final Exception e) {
            logger.error("Error while updating counter for instituteId {}, counterType {}, counterPrefix {}", instituteId,
                    counterType, counterPrefix, e);
        }

        return false;
    }

    public StandardMetadata getStandardMetaData(int instituteId, int academicSessionId, UUID standardId) {
        try {
            final Object[] args = {instituteId, academicSessionId, standardId.toString()};
            return jdbcTemplate.queryForObject(GET_STANDARD_META_DATA, args, STANDARD_META_DATA_ROW_MAPPER);
        } catch (final DataAccessException e) {
            logger.warn("No meta data exists for institute {}, session {}, standard {}", instituteId, academicSessionId,
                    standardId);
        } catch (final Exception e) {
            logger.error("Error occured while getting standard meta data for institute {}, session {}, standard {}",
                    instituteId, academicSessionId, standardId, e);
            throw new EmbrateRunTimeException("Error occured while getting standard meta data ", e);
        }
        return null;
    }

    public boolean addStandardMetadata(int instituteId, int academicSessionId, List<StandardMetadata> standardMetadataList) {
        try {
            List<Object []> args = new ArrayList<>();
            for(StandardMetadata standardMetaData : standardMetadataList){
                args.add(new Object[]{instituteId, academicSessionId, standardMetaData.getStandardId().toString(), standardMetaData.isCoScholasticGradingEnabled(), standardMetaData.isScholasticGradingEnabled()});
            }
            final int [] rows = jdbcTemplate.batchUpdate(CREATE_STANDARD_META_DATA, args);
            return rows.length == args.size();
        } catch (final Exception e) {
            logger.error("Exception while adding standard metadata for institute {}, academicSessionId {}",
                    instituteId, academicSessionId, e);
        }
        return false;
    }


    public AcademicSession getNextSessionDetails(int instituteId, int currentAcademicSessionId) {
        List<AcademicSession> academicSessionList = getAcademicSessionList(instituteId);
        if (CollectionUtils.isEmpty(academicSessionList)) {
            return null;
        }
        for (int i = 0; i < academicSessionList.size(); i++) {
            if (academicSessionList.get(i).getAcademicSessionId() == currentAcademicSessionId && i != 0) {
                return academicSessionList.get(i - 1);
            }
        }
        return null;
    }

    public AcademicSession getPreviousSessionDetails(int instituteId, int currentAcademicSessionId) {
        List<AcademicSession> academicSessionList = getAcademicSessionList(instituteId);
        if (CollectionUtils.isEmpty(academicSessionList)) {
            return null;
        }
        for (int i = 0; i < academicSessionList.size(); i++) {
            if (academicSessionList.get(i).getAcademicSessionId() == currentAcademicSessionId && i != academicSessionList.size() - 1) {
                return academicSessionList.get(i + 1);
            }
        }
        return null;
    }

    public AcademicSession getLatestSessionDetails(int instituteId) {
        List<AcademicSession> academicSessionList = getAcademicSessionListCache(instituteId);
        if (CollectionUtils.isEmpty(academicSessionList)) {
            return getAcademicSessionList(instituteId).get(0);
        }
        /**
         * assuming order of academic session is sorted
         */
        return academicSessionList.get(0);
    }

    public AcademicSession getCurrentDateSessionDetails(int instituteId) {
        List<AcademicSession> academicSessionList = getAcademicSessionList(instituteId);
        if (CollectionUtils.isEmpty(academicSessionList)) {
            return null;
        }
        for(AcademicSession academicSession : academicSessionList) {
            if(academicSession.isCurrentSession()) {
                return academicSession;
            }
        }
        return academicSessionList.get(0);
    }

    public List<AcademicSession> getAcademicSessionList(int instituteId) {
        if (instituteId <= 0) {
            return null;
        }
        List<AcademicSession> academicSessionLists = getAcademicSessionListCache(instituteId);
        if (!CollectionUtils.isEmpty(academicSessionLists)) {
            return academicSessionLists;
        }
        final Object[] args = {instituteId};
        try {
            List<AcademicSession> academicSessionList = jdbcTemplate.query(GET_SORTED_ACADEMIC_SESSION_BY_INSTITUTE_ID,
                    args, ACADEMIC_SESSION_ROW_MAPPER);
            sortAcademicSessionList(academicSessionList);
            setAcademicSessionListCache(instituteId, academicSessionList);
            return academicSessionList;

        } catch (final Exception e) {
            logger.error("Error while getting institute session for id {}", instituteId, e);
        }
        return null;
    }

    public AcademicSession getAcademicSession(int instituteId, int academicSessionId) {
        List<AcademicSession> academicSessionList = getAcademicSessionList(instituteId);
        if (CollectionUtils.isEmpty(academicSessionList)) {
            return null;
        }
        for (AcademicSession academicSession : academicSessionList) {
            if (academicSession.getAcademicSessionId() == academicSessionId) {
                return academicSession;
            }
        }
        return null;
    }

    private void sortAcademicSessionList(List<AcademicSession> academicSessionList) {
        Collections.sort(academicSessionList, new Comparator<AcademicSession>() {
                    @Override
                    public int compare(AcademicSession s1, AcademicSession s2) {
                        return s2.getSessionStartTime() - s1.getSessionStartTime();
                    }
                }
        );
    }

    public AcademicSession getAcademicSessionByAcademicSessionId(int academicSessionId) {
        if (academicSessionId <= 0) {
            return null;
        }
        AcademicSession academicSession = getAcademicSessionDetailsCache(academicSessionId);
        if (academicSession != null) {
            return academicSession;
        }
        List<Institute> instituteList = getAllInstitute();
        for (Institute institute : instituteList) {
            List<AcademicSession> academicSessionList = getAcademicSessionList(institute.getInstituteId());
            for (AcademicSession academicSession1 : academicSessionList) {
                if (academicSession1.getAcademicSessionId() == academicSessionId) {
                    return academicSession1;
                }
            }
        }
        return null;
    }

    private void invalidateCache(int instituteId) {
        if (ACADEMIC_SESSION_LOCAL_CACHE.containsKey(instituteId)) {
            ACADEMIC_SESSION_LOCAL_CACHE.remove(instituteId);
        }
    }

    private List<AcademicSession> getAcademicSessionListCache(int instituteId) {
        Map<Integer, AcademicSession> academicSessionMap = ACADEMIC_SESSION_LOCAL_CACHE.get(instituteId);
        if (CollectionUtils.isEmpty(academicSessionMap)) {
            return null;
        }
        List<AcademicSession> academicSessionList = new ArrayList<>(academicSessionMap.values());
        sortAcademicSessionList(academicSessionList);
        return academicSessionList;
    }

    private AcademicSession getAcademicSessionDetailsCache(int academicSessionId) {
        Map<Integer, Map<Integer, AcademicSession>> instituteAcademicSessionMap = ACADEMIC_SESSION_LOCAL_CACHE;
        if (CollectionUtils.isEmpty(instituteAcademicSessionMap)) {
            return null;
        }

        for (Map.Entry<Integer, Map<Integer, AcademicSession>> instituteMap : instituteAcademicSessionMap.entrySet()) {
            if (instituteMap != null && CollectionUtils.isEmpty(instituteMap.getValue())) {
                continue;
            }
            if (instituteMap.getValue().containsKey(academicSessionId)) {
                return instituteMap.getValue().get(academicSessionId);
            }
        }
        return null;
    }

    private void setAcademicSessionListCache(int instituteId, List<AcademicSession> academicSessionList) {
        invalidateCache(instituteId);
        if (CollectionUtils.isEmpty(academicSessionList)) {
            ACADEMIC_SESSION_LOCAL_CACHE.put(instituteId, new LinkedHashMap<>());
            return;
        }
        LinkedHashMap<Integer, AcademicSession> academicSessionMap = new LinkedHashMap<>();
        sortAcademicSessionList(academicSessionList);
        for (AcademicSession academicSession : academicSessionList) {
            academicSessionMap.put(academicSession.getAcademicSessionId(), academicSession);
        }
        ACADEMIC_SESSION_LOCAL_CACHE.put(instituteId, academicSessionMap);
    }

    public UUID addInstituteHouse(InstituteHouse instituteHouse, UUID userId) {
        UUID houseId = UUID.randomUUID();
        try {
            final int row = jdbcTemplate.update(ADD_INSTITUTE_HOUSE_DETAILS,
                    instituteHouse.getInstituteId(), houseId.toString(),
                    instituteHouse.getHouseName(), userId.toString());

            return row == 1 ? houseId : null;
        } catch (final Exception e) {
            logger.error("Exception while inserting staff details for institute {}, staffPaylaod {}",
                    instituteHouse.getInstituteId(), instituteHouse, e);
        } finally {
            invalidateInstituteHouseCache(instituteHouse.getInstituteId());
        }
        return null;
    }

    public InstituteHouse checkInstituteHouseByName(int instituteId, String houseName) {
        try {
            final Object[] args = {instituteId, houseName};
            return jdbcTemplate.queryForObject(GET_INSTITUTE_HOUSE_DETAILS_BY_NAME, args, INSTITUTE_HOUSES_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Exception while getting institute house for instituteId {}, houseName {}", instituteId,
                    houseName, e);
        }
        return null;
    }

    public InstituteHouse getInstituteHousesDetailsByHouseId(int instituteId, UUID houseId) {
        try {
            final Object[] args = {instituteId, houseId.toString()};
            return jdbcTemplate.queryForObject(GET_INSTITUTE_HOUSE_DETAILS_BY_ID, args, INSTITUTE_HOUSES_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Exception while getting institute house for instituteId {}, houseId {}", instituteId,
                    houseId, e);
        }
        return null;
    }

    public List<InstituteHouse> getInstituteHouseList(int instituteId) {
        return instituteHouseCache.get(new InstituteHouseCacheKey(instituteId));
    }

    private List<InstituteHouse> getInstituteHouseListUnCached(int instituteId) {
        try {
            final Object[] args = {instituteId};
            return jdbcTemplate.query(GET_INSTITUTE_HOUSE_LIST, args, INSTITUTE_HOUSES_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Exception while getting institute house for instituteId {}", instituteId, e);
        }
        return null;
    }

    public boolean updateInstituteHouse(InstituteHouse instituteHouse, UUID userId) {
        try {
            final Object[] args = {instituteHouse.getHouseName(), userId.toString(),
                    instituteHouse.getInstituteId(), instituteHouse.getHouseId().toString()};
            return jdbcTemplate.update(UPDATE_INSTITUTE_HOUSE_DETAILS, args) == 1;
        } catch (final Exception e) {
            logger.error("Exception while inserting staff details for institute {}, staffPaylaod {}",
                    instituteHouse.getInstituteId(), instituteHouse, e);
        } finally {
            invalidateInstituteHouseCache(instituteHouse.getInstituteId());
        }
        return false;
    }

    public boolean deleteInstituteHouse(int instituteId, UUID houseId) {
        final Object[] args = {instituteId, houseId.toString()};
        try {
            return jdbcTemplate.update(DELETE_INSTITUTE_HOUSE_BY_ID, args) == 1;
        } catch (final Exception e) {
            logger.error("Exception while deleting house with id {}, instituteId {}", houseId, instituteId, e);
        } finally {
            invalidateInstituteHouseCache(instituteId);
        }
        return false;
    }

    public List<InstituteHousesWithCount> getInstituteHousesWithCount(int instituteId, int academicSessionId) {
        try {
            final Object[] args = {academicSessionId, instituteId};
            return jdbcTemplate.query(GET_SESSION_STUDENT_COUNT_BY_HOUSE, args, INSTITUTE_HOUSES_WITH_COUNT_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Exception while getting institute house for instituteId {}", instituteId, e);
        }
        return null;
    }

    public List<InstituteHousesWithCount> getInstituteHousesWithCount(int instituteId) {
        try {
            final Object[] args = {instituteId};
            return jdbcTemplate.query(GET_INSTITUTE_STUDENT_COUNT_BY_HOUSE, args, INSTITUTE_HOUSES_WITH_COUNT_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Exception while getting institute house for instituteId {}", instituteId, e);
        }
        return null;
    }

    private class InstituteHouseCacheKey implements ICacheKey {

        private final int instituteId;

        public InstituteHouseCacheKey(int instituteId) {
            this.instituteId = instituteId;
        }


        public int getInstituteId() {
            return instituteId;
        }

        @Override
        public String getKeyId() {
            return String.valueOf(instituteId);
        }
    }

    private class InstituteHouseCacheLoader implements ICacheLoader<InstituteHouseCacheKey, List<InstituteHouse>> {

        @Override
        public List<InstituteHouse> load(InstituteHouseCacheKey key) {
            return getInstituteHouseListUnCached(key.getInstituteId());
        }
    }

    private void invalidateInstituteHouseCache(int instituteId){
        instituteHouseCache.delete(new InstituteHouseCacheKey(instituteId));
    }

    private class InstituteCacheKey implements ICacheKey {

        private final int instituteId;

        public InstituteCacheKey(int instituteId) {
            this.instituteId = instituteId;
        }


        public int getInstituteId() {
            return instituteId;
        }

        @Override
        public String getKeyId() {
            return String.valueOf(instituteId);
        }
    }
    private class InstituteCacheLoader implements ICacheLoader<InstituteCacheKey, Institute> {

        @Override
        public Institute load(InstituteCacheKey key) {
            return getInstituteListUnCached(key.getInstituteId());
        }

    }

    private void invalidateInstituteCache(int instituteId){
        instituteCache.delete(new InstituteCacheKey(instituteId));
    }

    public List<StandardWithStaffDetails> getStandardWithStaffDetailsList(int instituteId, int academicSessionId, UUID staffId, UUID standardId) {
        try {
            final List<Object> args = new ArrayList<>();
            args.add(academicSessionId);
            args.add(academicSessionId);
            args.add(instituteId);
            StringBuilder inQuery = new StringBuilder();
            if(staffId != null) {
                args.add(staffId.toString());
                inQuery.append(" and standard_session_data.class_teacher_staff_id = ? ");
            }
            if(standardId != null) {
                args.add(standardId.toString());
                inQuery.append(" and standard_session_data.standard_id = ? ");
            }
            return jdbcTemplate.query(String.format(GET_STANDARD_STAFF_DETAILS, inQuery), args.toArray(), STANDARD_ROW_DETAILS_WITH_STAFF_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Exception while getting standard staff details for instituteId {}, academicSessionId {}", instituteId, academicSessionId, e);
        }
        return null;
    }

    public List<StandardSessionDataPayload> getStandardSessionDataPayloadList(int instituteId, int academicSessionId, UUID standardId, Set<Integer> sectionIdSet, UUID staffId) {
        try {
            final List<Object> args = new ArrayList<>();
            args.add(instituteId);
            args.add(academicSessionId);
            StringBuilder standardInQuery = new StringBuilder();
            if(standardId != null) {
                args.add(standardId.toString());
                standardInQuery.append(" and standard_session_data.standard_id = ? ");
            }
            StringBuilder sectionInQuery = new StringBuilder();
            if(!CollectionUtils.isEmpty(sectionIdSet)) {
                sectionInQuery.append(" and ");
                sectionInQuery.append(" section_id in ");
                sectionInQuery.append(" (");
                boolean firstStandard = true;
                for (final Integer sectionId : sectionIdSet) {
                    if(sectionId == null || sectionId <= 0) {
                        continue;
                    }
                    args.add(sectionId);
                    if (firstStandard) {
                        sectionInQuery.append("?");
                        firstStandard = false;
                        continue;
                    }
                    sectionInQuery.append(", ?");
                }
                sectionInQuery.append(") ");
            }
            StringBuilder staffInQuery = new StringBuilder();
            if(staffId != null) {
                args.add(staffId.toString());
                staffInQuery.append(" and standard_session_data.class_teacher_staff_id = ? ");
            }
            return jdbcTemplate.query(String.format(GET_STANDARD_SESSION_DATA, standardInQuery, sectionInQuery, staffInQuery), args.toArray(),
                    STANDARD_SESSION_DATA_PAYLOAD_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Exception while getting standard staff details for instituteId {}, academicSessionId {}", instituteId, academicSessionId, e);
        }
        return null;
    }

    public boolean addStandardStaffAssignment(int instituteId, int academicSessionId, UUID staffId, UUID standardId, Integer sectionId) {
        try {
            return jdbcTemplate.update(INSERT_STANDARD_SESSION_DATA,
                    instituteId, academicSessionId, standardId.toString(), sectionId, staffId == null ? null : staffId.toString()) == 1;
        } catch (final Exception e) {
            e.printStackTrace();
            logger.error("Error while adding standard staff assignment for instituteId {}, academicSessionId {}, standardId {}, sectionId {}",
                    instituteId, academicSessionId, standardId, sectionId);
        }
        return false;
    }

    public boolean upsertBulkStandardStaffAssignment(List<StandardSessionDataPayload> standardSessionDataPayloadList){
        try {
            List<Object[]> args = new ArrayList<>();

        for (StandardSessionDataPayload standardSessionDataPayload : standardSessionDataPayloadList) {
            args.add(new Object[]{
                standardSessionDataPayload.getInstituteId(),
                standardSessionDataPayload.getAcademicSessionId(),
                standardSessionDataPayload.getStandardId().toString(),
                standardSessionDataPayload.getSectionId(),
                standardSessionDataPayload.getStaffId() != null ? standardSessionDataPayload.getStaffId().toString() : null,
            });
        }
        return jdbcTemplate.batchUpdate(UPSERT_STANDARD_SESSION_DATA, args).length == standardSessionDataPayloadList.size();
        } catch (final Exception e) {
            logger.error("Error while adding standard staff assignment for instituteId {}, academicSessionId {} ",
            standardSessionDataPayloadList.get(0).getInstituteId(), standardSessionDataPayloadList.get(0).getAcademicSessionId());
        }
        return false;
    }

    public boolean updateStandardStaffAssignment(int instituteId, int academicSessionId, UUID staffId, UUID standardId, Set<Integer> sectionIdSet) {
        try {
            final List<Object> updateArgs = new ArrayList<>();
            updateArgs.add(staffId == null ? null : staffId.toString());
            updateArgs.add(instituteId);
            updateArgs.add(academicSessionId);
            updateArgs.add(standardId.toString());
            StringBuilder updateInQuery = new StringBuilder();
            if(!CollectionUtils.isEmpty(sectionIdSet)) {
                updateInQuery.append(" and ");
                updateInQuery.append(" section_id in ");
                updateInQuery.append(" (");
                boolean firstStandard = true;
                for (final Integer sectionId : sectionIdSet) {
                    if(sectionId == null || sectionId <= 0) {
                        continue;
                    }
                    updateArgs.add(sectionId);
                    if (firstStandard) {
                        updateInQuery.append("?");
                        firstStandard = false;
                        continue;
                    }
                    updateInQuery.append(", ?");
                }
                updateInQuery.append(") ");
            }
            return jdbcTemplate.update(String.format(UPDATE_STANDARD_SESSION_DATA, updateInQuery), updateArgs.toArray()) >= 0;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("Error while deleting standard staff assignment for instituteId {}, academicSessionId {}, standardId {}, sectionIdSet {}",
                    instituteId, academicSessionId, standardId, sectionIdSet);
        }
        return false;
    }

    public boolean deleteStandardStaffAssignment(int instituteId, int academicSessionId, UUID standardId, Set<Integer> sectionIdSet) {
        try {
            final List<Object> deleteArgs = new ArrayList<>();
            deleteArgs.add(instituteId);
            deleteArgs.add(academicSessionId);
            deleteArgs.add(standardId.toString());
            StringBuilder deleteInQuery = new StringBuilder();
            if(!CollectionUtils.isEmpty(sectionIdSet)) {
                deleteInQuery.append(" and ");
                deleteInQuery.append(" section_id in ");
                deleteInQuery.append(" (");
                boolean firstStandard = true;
                for (final Integer sectionId : sectionIdSet) {
                    if(sectionId == null || sectionId <= 0) {
                        continue;
                    }
                    deleteArgs.add(sectionId);
                    if (firstStandard) {
                        deleteInQuery.append("?");
                        firstStandard = false;
                        continue;
                    }
                    deleteInQuery.append(", ?");
                }
                deleteInQuery.append(") ");
            }
            return jdbcTemplate.update(String.format(DELETE_STANDARD_SESSION_DATA, deleteInQuery), deleteArgs.toArray()) >= 0;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("Error while deleting standard staff assignment for instituteId {}, academicSessionId {}, standardId {}, sectionIdSet {}",
                    instituteId, academicSessionId, standardId, sectionIdSet);
        }
        return false;
    }

    public boolean updateStandardSessionDocuments(int instituteId, int academicSessionId, UUID standardId, Integer sectionId, List<Document<StandardSessionDocumentType>> standardSessionDocumentList,
                                                  boolean noEntryInDB){
        try {
            /**
             * adding this entry as no data is found in DB for this institute + session + standard + section
             */
            if(noEntryInDB) {
                addStandardStaffAssignment(instituteId, academicSessionId, null, standardId, sectionId);
            }
            if(sectionId == null || sectionId <= 0) {
                final Object[] args = { GSON.toJson(standardSessionDocumentList), instituteId, academicSessionId, standardId.toString()};
                return jdbcTemplate.update(String.format(UPDATE_STANDARD_SESSION_DOCUMENTS, " and standard_session_data.section_id is null "), args) == 1;
            }
            final Object[] args = { GSON.toJson(standardSessionDocumentList), instituteId, academicSessionId, standardId.toString(), sectionId};
            return jdbcTemplate.update(String.format(UPDATE_STANDARD_SESSION_DOCUMENTS, " and standard_session_data.section_id = ? "), args) == 1;
        } catch (final DataAccessException dataAccessException) {
            ExceptionHandling.HandleException(dataAccessException, null, null);
            logger.error("Error while updating institute documents {}", instituteId);
        } catch (final Exception e) {
            e.printStackTrace();
            logger.error("Exception while updating institute documents {}", instituteId, e);
        }
        return false;
    }

    public Standard getStandardByStandardId(int instituteId, int academicSessionId, UUID standardId) {
        if (instituteId <= 0) {
            return null;
        }
        if (academicSessionId <= 0) {
            return null;
        }
        if (standardId == null ) {
            return null;
        }

        List<Standard> standardList = getInstituteStandardList(instituteId, academicSessionId);
        if(CollectionUtils.isEmpty(standardList)) {
            return null;
        }
        for(Standard standard : standardList) {
            if(standard == null) {
                continue;
            }
            if(standard.getStandardId().equals(standardId)) {
                return standard;
            }
        }
        return null;
    }

    public UUID addInstituteBankAccount(InstituteBankAccountDetails instituteBankAccountDetailsPayload, UUID userId) {
        UUID accountId = UUID.randomUUID();
        try {
            final int row = jdbcTemplate.update(ADD_INSTITUTE_BANK_ACCOUNT,
                    instituteBankAccountDetailsPayload.getInstituteId(), accountId.toString(),
                    instituteBankAccountDetailsPayload.getAccountHolderName(), instituteBankAccountDetailsPayload.getBankName(),
                    instituteBankAccountDetailsPayload.getBranchName(), instituteBankAccountDetailsPayload.getAccountNumber(),
                    instituteBankAccountDetailsPayload.getIfscCode(), instituteBankAccountDetailsPayload.getAccountType()==null? null: instituteBankAccountDetailsPayload.getAccountType().name(),
                    instituteBankAccountDetailsPayload.getStatus() ==null? null: instituteBankAccountDetailsPayload.getStatus().name(), instituteBankAccountDetailsPayload.isPrimary(), new Timestamp(System.currentTimeMillis()));

            return row == 1 ? accountId : null;
        } catch (final Exception e) {
            logger.error("Exception while inserting staff details for institute {}, instituteBankAccountDetailsPayload {}",
                    instituteBankAccountDetailsPayload.getInstituteId(), instituteBankAccountDetailsPayload, e);
        }
        return null;
    }

    public List<InstituteBankAccountDetails> getInstituteBankAccounts(int instituteId) {
        try {
            final List<Object> args = new ArrayList<>();
            args.add(instituteId);
            return jdbcTemplate.query(GET_INSTITUTE_BANK_ACCOUNTS, args.toArray(), INSTITUTE_BANK_ACCOUNT_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Exception while getting institute bank accounts for instituteId {}", instituteId, e);
        }
        return null;
    }

    public InstituteBankAccountDetails getInstituteBankAccount(int instituteId, UUID accountId) {
        try {
            final List<Object> args = new ArrayList<>();
            args.add(instituteId);
            args.add(accountId.toString());

            String query = GET_INSTITUTE_BANK_ACCOUNTS + ACCOUNT_ID_CLAUSE;

            return jdbcTemplate.queryForObject(query, args.toArray(), INSTITUTE_BANK_ACCOUNT_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Exception while getting institute bank accounts for instituteId {}", instituteId, e);
        }
        return null;
    }

    public List<InstituteBankAccountDetails> getInstituteBankAccounts(int instituteId, Set<UUID> accountIds) {
        try {
            final List<Object> args = new ArrayList<>();
            args.add(instituteId);
            StringBuilder accountIdQuery = new StringBuilder();
            String delimiter = "";

            for(UUID accountId : accountIds){
                args.add(accountId.toString());
                accountIdQuery.append(delimiter).append(" ?");
                delimiter = ",";
            }


            String query = GET_INSTITUTE_BANK_ACCOUNTS + ACCOUNT_IDS_CLAUSE;

            return jdbcTemplate.query(String.format(query, accountIdQuery), args.toArray(), INSTITUTE_BANK_ACCOUNT_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Exception while getting institute bank accounts for instituteId {}", instituteId, e);
        }
        return null;
    }

    public boolean updateInstituteBankAccountPrimaryStatus(int instituteId, UUID existingIsPrimaryIds, boolean isPrimary){
        try{
                final List<Object> args = new ArrayList<>();
                args.add(isPrimary);
                args.add(new Timestamp(System.currentTimeMillis()));
                args.add(instituteId);
                args.add(existingIsPrimaryIds.toString());
            return jdbcTemplate.update(UPDATE_INSTITUTE_BANK_PRIMARY_STATUS, args.toArray()) == 1;
        }   catch (final Exception e){
            logger.error("Unable to update bank account primary status for accountId {}", existingIsPrimaryIds , e);
        }
        return false;
    }

    public boolean updateInstituteBankAccountDetails(InstituteBankAccountDetails instituteBankAccountDetailsPayload){
        try{
            final List<Object> args = new ArrayList<>();
            args.add(instituteBankAccountDetailsPayload.getAccountHolderName());
            args.add(instituteBankAccountDetailsPayload.getBankName());
            args.add(instituteBankAccountDetailsPayload.getBranchName());
            args.add(instituteBankAccountDetailsPayload.getAccountNumber());
            args.add(instituteBankAccountDetailsPayload.getIfscCode());
            args.add(instituteBankAccountDetailsPayload.getAccountType() == null? null: instituteBankAccountDetailsPayload.getAccountType().name());
            args.add(instituteBankAccountDetailsPayload.isPrimary());
            args.add(instituteBankAccountDetailsPayload.getStatus() == null? null: instituteBankAccountDetailsPayload.getStatus().name());
            args.add(new Timestamp(System.currentTimeMillis()));
            args.add(instituteBankAccountDetailsPayload.getInstituteId());
            args.add(instituteBankAccountDetailsPayload.getAccountId().toString());
            return jdbcTemplate.update(UPDATE_INSTITUTE_BANK_ACCOUNT_DETAILS, args.toArray()) == 1 ;
        }   catch (final Exception e){
            logger.error("Unable to update institute bank account {}", instituteBankAccountDetailsPayload.getAccountId() , e);
        }
        return false;
    }

    public boolean updateActiveStatusOfBankAccount(int instituteId,UUID accountId, BankAccountStatus bankAccountStatus){
        try {

            final List<Object> args = new ArrayList<>();
            args.add(bankAccountStatus.name());
            args.add(new Timestamp(System.currentTimeMillis()));
            args.add(instituteId);
            args.add(accountId.toString());

            return jdbcTemplate.update(UPDATE_ACTIVE_STATUS_INSTITUTE_BANK_PRIMARY_STATUS, args.toArray()) >= 0;
        }
        catch (final Exception e){
            logger.error("Unable to delete institute bank account for institute {} accountId {}", instituteId, accountId, e);
        }
        return false;
    }
}
