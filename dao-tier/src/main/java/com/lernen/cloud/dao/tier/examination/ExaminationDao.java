package com.lernen.cloud.dao.tier.examination;

import com.embrate.cloud.core.api.examination.management.GradeRenamePayload;
import com.embrate.cloud.core.api.examination.management.GradeUpdatePayload;
import com.embrate.cloud.core.api.examination.utility.ExaminationGradesPayloadReadable;
import com.embrate.cloud.core.api.examination.utility.StandardExaminationGrades;
import com.embrate.cloud.core.api.institute.StandardMetadata;
import com.google.gson.Gson;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.examination.*;
import com.lernen.cloud.core.api.examination.report.*;
import com.lernen.cloud.core.api.examination.report.greensheet.GreenSheetClassStructure;
import com.lernen.cloud.core.api.examination.report.greensheet.GreenSheetExamDimensionMapData;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.PlaceholdersUtils;
import com.lernen.cloud.core.utils.UUIDUtils;
import com.lernen.cloud.dao.tier.examination.mapper.*;
import com.lernen.cloud.dao.tier.institute.mappers.StandardMetadataRowMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.checkerframework.checker.units.qual.C;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.Map.Entry;

/**
 * <AUTHOR>
 */
public class ExaminationDao {

	private static final Logger logger = LogManager.getLogger(ExaminationDao.class);

	private static final ExamDimensionRowMapper EXAM_DIMENSION_ROW_MAPPER = new ExamDimensionRowMapper();

	private static final ExamDetailsRowMapper EXAM_DETAILS_ROW_MAPPER = new ExamDetailsRowMapper();

	private static final ExamCourseRowMapper EXAM_COURSE_ROW_MAPPER = new ExamCourseRowMapper();

	private static final ExamMarksDetailsRowMapper EXAM_MARKS_DETAILS_ROW_MAPPER = new ExamMarksDetailsRowMapper();

	private static final ExamDimensionMappingMapper EXAM_DIMENSION_MAPPING_MAPPER = new ExamDimensionMappingMapper();

	private static final ExamNodeEdgeRowMapper EXAM_NODE_ROW_MAPPER = new ExamNodeEdgeRowMapper();

	private static final ExamNodeEdgeWithCoursesRowMapper EXAM_NODE_EDGE_WITH_COURSES_ROW_MAPPER = new ExamNodeEdgeWithCoursesRowMapper();

	private static final MarksFeedingMapper MARKS_FEEDING_MAPPER = new MarksFeedingMapper();

	private static final ExamCoursesAssigmentMapper EXAM_COURSES_ASSIGMENT_MAPPER = new ExamCoursesAssigmentMapper();

	private static final ExamMetaDataRowMapper EXAM_METADATA_MAPPER = new ExamMetaDataRowMapper();

	private static final ExamReportStructureRowMapper EXAM_REPORT_STRUCTURE_ROW_MAPPER = new ExamReportStructureRowMapper();

	private static final ExamReportCardTypeRowMapper EXAM_REPORT_CARD_TYPE_ROW_MAPPER = new ExamReportCardTypeRowMapper();

//	private static final ReportCardVariablesRowMapper REPORT_CARD_VARIABLES_ROW_MAPPER = new ReportCardVariablesRowMapper();

	private static final StandardMetadataRowMapper STANDARD_META_DATA_ROW_MAPPER = new StandardMetadataRowMapper();

	private static final ExamGradeRowMapper EXAM_GRADE_ROW_MAPPER = new ExamGradeRowMapper();

	private static final ReportCardVariableDetailsRowMapper REPORT_CARD_VARIABLE_DETAILS_ROW_MAPPER = new ReportCardVariableDetailsRowMapper();

	private static final ExamGreenSheetStructureRowMapper EXAM_GREEN_SHEET_STRUCTURE_ROW_MAPPER = new ExamGreenSheetStructureRowMapper();

	private static final DatesheetMetadataRowMapper DATESHEET_METADATA_ROW_MAPPER = new DatesheetMetadataRowMapper();

	private static final DatesheetDetailsRowMapper DATESHEET_DETAILS_ROW_MAPPER = new DatesheetDetailsRowMapper();
	private static final PersonalityTraitsRowMapper PERSONALITY_TRAITS_ROW_MAPPER = new PersonalityTraitsRowMapper();
	private static final StudentPersonalityTraitsDetailsRowMapper STUDENT_PERSONALITY_TRAITS_DETAILS_ROW_MAPPER = new StudentPersonalityTraitsDetailsRowMapper();
	private static final StudentReportCardStatusDetailsRowMapper STUDENT_REPORT_CARD_STATUS_DETAILS_ROW_MAPPER = new StudentReportCardStatusDetailsRowMapper();
	private static final StudentPersonalityTraitUsageInfoRowMapper STUDENT_PERSONALITY_TRAIT_USAGE_INFO_ROW_MAPPER = new StudentPersonalityTraitUsageInfoRowMapper();

	private static final String GET_EXAM_METADATA = "select * from exam_metadata where exam_id = ?";

	private static final String GET_SYSTEM_EXAM_METADATA = "select * from exam_metadata where academic_session_id = ? and standard_id = ? and exam_type = 'SYSTEM'";

	private static final String GET_EXAM_METADATA_BY_INSTITUTE = "select * from exam_metadata where "
			+ "standard_id in (select standard_id from standards where institute_id = ?) order by exam_name ";

	private static final String UPDATE_EXAM_META_DATA = "update exam_metadata set exam_name = ?, operation = ?, " +
			" attendance_start_date = ?, attendance_end_date = ?, exam_start_date = ?, " +
			" date_of_result_declaration = ?, sch_display_type = ?, " +
			" cosch_display_type = ? where exam_id = ?";
	private static final String UPDATE_PARENT_EXAM_IDS_IN_META_DATA = "update exam_metadata set parent_exam_ids = ? where exam_id = ?";

	private static final String FEED_MARKS = "insert into marks_feeding(student_id, exam_id, course_id, dimension_id, marks_obtained, grade_obtained, grace_marks, attendance_status, status) "
			+ "values(?, ?, ?, ?, ?, ?, ?, ?, ?)";

	private static final String UPDATE_FEED_MARKS_STATUS = "update marks_feeding set status = ? where exam_id = ? and course_id = ? and dimension_id = ?";

	private static final String UPDATE_FEED_MARKS_STATUS_WITH_SECTION = "update marks_feeding "
			+ " inner join student_academic_session_details on (student_academic_session_details.student_id = marks_feeding.student_id and student_academic_session_details.section_id = ? and student_academic_session_details.academic_session_id = ?) "
			+ " set status = ? where exam_id = ? and course_id = ? and dimension_id = ?";

	private static final String DELETE_FEED_MARKS = "delete from marks_feeding where student_id = ? and exam_id = ? and course_id = ? and dimension_id = ?";

	private static final String DELETE_FEED_MARKS_BY_EXAM_ID = "delete from marks_feeding where exam_id = ? ";

	private static final String ADD_EXAM_DIMENSION = "insert into exam_dimensions(institute_id, dimension_name, dimension_type, evaluation_type, "
			+ "is_total) values(?, ?, ?, ?, ?)";

	private static final String UPDATE_EXAM_DIMENSION = "update exam_dimensions set dimension_name = ?, dimension_type = ?, evaluation_type = ?, is_total = ? where institute_id = ? and dimension_id = ?";

	private static final String DELETE_EXAM_DIMENSION = "delete from exam_dimensions where institute_id = ? and dimension_id = ?";

	private static final String DELETE_EXAM = "delete from exam_metadata where exam_id = ?";

	private static final String GET_EXAM_DIMENSION_BY_INSTITUTE = "select * from exam_dimensions where institute_id = ?";

	private static final String GET_EXAM_DIMENSIONS_MAPPING_BY_EXAM_ID = "select exam_dimensions.*, exam_dimensions_mapping.* , exam_courses_assignment.* from exam_dimensions_mapping inner join "
			+ "exam_dimensions on exam_dimensions_mapping.dimension_id = exam_dimensions.dimension_id inner join exam_courses_assignment on "
			+ "exam_courses_assignment.exam_id = exam_dimensions_mapping.exam_id where exam_dimensions_mapping.exam_id = ?";

	private static final String GET_MARKS_BY_COURSE_ID = "select * from marks_feeding where student_id = ? and exam_id = ? and course_id = ?";

	private static final String GET_MARKS_BY_STUDENT_ID = "select * from marks_feeding "
			+ " inner join exam_metadata on exam_metadata.exam_id = marks_feeding.exam_id "
			+ " where academic_session_id = ? and marks_feeding.student_id = ?";

	private static final String ADD_EXAM_METADATA = "insert into exam_metadata(exam_id, exam_name, exam_type, standard_id, academic_session_id, "
			+ "parent_exam_ids, operation, attendance_start_date, attendance_end_date, exam_start_date, " +
			" date_of_result_declaration, sch_display_type, cosch_display_type) values(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

	private static final String DELETE_EXAM_COURSES = "delete from exam_courses_assignment where exam_id = ? and course_id in %s";

	private static final String DELETE_EXAM_COURSES_ASSIGMENT_BY_EXAM_ID = "delete from exam_courses_assignment where exam_id = ?";

	private static final String GET_EXAM_COURSES_ASSIGMENT_BY_EXAM_ID = "select exam_dimensions.*, exam_courses_assignment.* from exam_courses_assignment inner join exam_dimensions on exam_courses_assignment.dimension_id = exam_dimensions.dimension_id where exam_id = ?";

	private static final String UPDATE_EXAM_COURSES = "update exam_courses_assignment set max_marks = ?, min_marks = ?, max_grade = ?, min_grade = ? where exam_id = ? and course_id = ? and  dimension_id = ?";
	private static String GET_EXAM_COURSES_BY_TYPE = "select exam_metadata.*, class_courses.*, exam_dimensions.*, "
			+ "exam_courses_assignment.* from exam_metadata join exam_courses_assignment on exam_metadata.exam_id = "
			+ "exam_courses_assignment.exam_id join "
			+ "class_courses on exam_courses_assignment.course_id = class_courses.course_id join exam_dimensions on "
			+ "exam_dimensions.dimension_id = exam_courses_assignment.dimension_id where exam_metadata.exam_id = ? and class_courses.course_type = ?";

	private static final String CHECK_PARENT_EXAM = "select exam_metadata.*, exam_graph_edges.* from exam_graph_edges inner join exam_metadata on exam_metadata.exam_id = exam_graph_edges.exam_id where exam_graph_edges.exam_id = ?";

	private static final String CHECK_CHILD_EXAM = "select exam_metadata.*, exam_graph_edges.* from exam_graph_edges inner join exam_metadata on exam_metadata.exam_id = exam_graph_edges.child_exam_id where child_exam_id = ?";

	private static final String DELETE_EXAM_GRAPH_EDGES_BY_EXAM_ID = "delete from exam_graph_edges where child_exam_id = ?";

	private static final String GET_ALL_EXAM_COURSES_FOR_STANDARD = "select exam_metadata.*, class_courses.*, exam_dimensions.*, "
			+ "exam_courses_assignment.* from exam_metadata join exam_courses_assignment on exam_metadata.exam_id = "
			+ "exam_courses_assignment.exam_id join "
			+ "class_courses on exam_courses_assignment.course_id = class_courses.course_id join exam_dimensions on "
			+ "exam_dimensions.dimension_id = exam_courses_assignment.dimension_id where exam_metadata.standard_id = ? and exam_metadata.academic_session_id = ?";

	private static final String GET_DIMENSIONS_COURSES = "select * from exam_courses_assignment where course_id = ?";

	private static final String ADD_EXAM_DIMENSION_MAPPINGS = "insert into exam_dimensions_mapping(exam_id, dimension_id, course_type, "
			+ "default_max_marks, default_min_marks, default_max_grade, default_min_grade) values(?, ?, ?, ?, ?, ?, ?)";

	private static final String DELETE_EXAM_DIMENSION_MAPPING = "delete from exam_dimensions_mapping where exam_id = ? and dimension_id in "
			+ "(select dimension_id from exam_dimensions where institute_id = ?)";

	private static final String DELETE_EXAM_DIMENSIONS_MAPPING_BY_EXAM_ID = "delete from exam_dimensions_mapping where exam_id = ?";

	private static final String ADD_EXAM_GRAPH_EDGE = "insert into exam_graph_edges(exam_id, child_exam_id) values (?, ?)";

	private static final String ADD_EXAM_COURSES = "insert into exam_courses_assignment(exam_id, course_id, dimension_id, max_marks, min_marks, "
			+ "max_grade, min_grade, status) values(?, ?, ?, ?, ?, ?, ?, ?)";

	private static final String GET_EXAM_DETAILS = "select exam_metadata.*, exam_dimensions.*, exam_dimensions_mapping.*, exam_courses_assignment.*, "
			+ "class_courses.*, standards.*, standard_section_mapping.*, academic_session.*, standards_metadata.* from exam_metadata join exam_dimensions_mapping on "
			+ "exam_metadata.exam_id= exam_dimensions_mapping.exam_id join "
			+ "exam_dimensions on exam_dimensions_mapping.dimension_id= exam_dimensions.dimension_id left join exam_courses_assignment on "
			+ "exam_dimensions_mapping.exam_id= exam_courses_assignment.exam_id and exam_dimensions_mapping.dimension_id = "
			+ "exam_courses_assignment.dimension_id left join class_courses on exam_courses_assignment.course_id = class_courses.course_id and "
			+ "exam_dimensions_mapping.course_type= class_courses.course_type join academic_session on exam_metadata.academic_session_id = "
			+ "academic_session.academic_session_id join standards on exam_metadata.standard_id = "
			+ "standards.standard_id left join standard_section_mapping on standards.standard_id = "
			+ "standard_section_mapping.standard_id and standard_section_mapping.academic_session_id = exam_metadata.academic_session_id "
			+ "left join standards_metadata on academic_session.academic_session_id = "
			+ "standards_metadata.academic_session_id and standards.standard_id = standards_metadata.standard_id where "
			+ "exam_metadata.exam_id = ? and standards.institute_id = ?";

	private static final String GET_CLASS_SESSION_EXAM_DETAILS = "select exam_metadata.*, exam_dimensions.*, exam_dimensions_mapping.*, exam_courses_assignment.*, "
			+ "class_courses.*, standards.*, standard_section_mapping.*, academic_session.*, standards_metadata.* from exam_metadata join exam_dimensions_mapping on "
			+ "exam_metadata.exam_id= exam_dimensions_mapping.exam_id join "
			+ "exam_dimensions on exam_dimensions_mapping.dimension_id= exam_dimensions.dimension_id left join exam_courses_assignment on "
			+ "exam_dimensions_mapping.exam_id= exam_courses_assignment.exam_id and exam_dimensions_mapping.dimension_id = "
			+ "exam_courses_assignment.dimension_id left join class_courses on exam_courses_assignment.course_id = class_courses.course_id and "
			+ "exam_dimensions_mapping.course_type= class_courses.course_type join academic_session on exam_metadata.academic_session_id = "
			+ "academic_session.academic_session_id join standards on exam_metadata.standard_id = "
			+ "standards.standard_id left join standard_section_mapping on standards.standard_id = "
			+ "standard_section_mapping.standard_id and standard_section_mapping.academic_session_id = exam_metadata.academic_session_id "
			+ "left join standards_metadata on academic_session.academic_session_id = "
			+ "standards_metadata.academic_session_id and standards.standard_id = standards_metadata.standard_id "
			+ "where exam_metadata.standard_id = ? and exam_metadata.academic_session_id = ? "
			+ "and standards.institute_id = ?";

	private static final String GET_CLASS_SESSION_EXAM_DETAILS_BY_EXAM_ID = "select exam_metadata.*, exam_dimensions.*, exam_dimensions_mapping.*, exam_courses_assignment.*, "
			+ "class_courses.*, standards.*, standard_section_mapping.*, academic_session.*, standards_metadata.* from exam_metadata join exam_dimensions_mapping on "
			+ "exam_metadata.exam_id= exam_dimensions_mapping.exam_id join "
			+ "exam_dimensions on exam_dimensions_mapping.dimension_id= exam_dimensions.dimension_id left join exam_courses_assignment on "
			+ "exam_dimensions_mapping.exam_id= exam_courses_assignment.exam_id and exam_dimensions_mapping.dimension_id = "
			+ "exam_courses_assignment.dimension_id left join class_courses on exam_courses_assignment.course_id = class_courses.course_id and "
			+ "exam_dimensions_mapping.course_type= class_courses.course_type join academic_session on exam_metadata.academic_session_id = "
			+ "academic_session.academic_session_id join standards on exam_metadata.standard_id = "
			+ "standards.standard_id left join standard_section_mapping on standards.standard_id = "
			+ "standard_section_mapping.standard_id and standard_section_mapping.academic_session_id = exam_metadata.academic_session_id "
			+ "left join standards_metadata on academic_session.academic_session_id = "
			+ "standards_metadata.academic_session_id and standards.standard_id = standards_metadata.standard_id "
			+ "where exam_metadata.standard_id in (select standard_id from exam_metadata where exam_id = ?) and "
			+ "exam_metadata.academic_session_id = (select academic_session_id from exam_metadata where exam_id = ?) "
			+ "and standards.institute_id = ?";

	private static final String GET_CLASS_EXAM_GRAPH_BY_EXAM = "select exam_metadata.*, exam_graph_edges.child_exam_id from exam_metadata left join "
			+ "exam_graph_edges on exam_metadata.exam_id = exam_graph_edges.exam_id where exam_metadata.standard_id in "
			+ "(select standard_id from exam_metadata where exam_id = ?) and "
			+ "exam_metadata.academic_session_id in (select academic_session_id from exam_metadata where exam_id = ?) "
			+ "and standard_id in (select standard_id from standards where institute_id = ?)";

	private static final String GET_CLASS_EXAM_GRAPH_WITH_COURSES_BY_EXAM = "select exam_metadata.*, class_courses.*, exam_graph_edges.child_exam_id from "
			+ "exam_metadata left join exam_courses_assignment on exam_metadata.exam_id = exam_courses_assignment.exam_id left join "
			+ "class_courses on exam_courses_assignment.course_id = class_courses.course_id left join "
			+ "exam_graph_edges on exam_metadata.exam_id = exam_graph_edges.exam_id where exam_metadata.standard_id in "
			+ "(select standard_id from exam_metadata where exam_id = ?) and "
			+ "exam_metadata.academic_session_id in (select academic_session_id from exam_metadata where exam_id = ?) and "
			+ "exam_metadata.standard_id in (select standard_id from standards where institute_id = ?)";

	private static final String GET_CLASS_EXAM_GRAPH_BY_STANDARD = "select exam_metadata.*, exam_graph_edges.child_exam_id from exam_metadata left join "
			+ "exam_graph_edges on exam_metadata.exam_id = exam_graph_edges.exam_id where exam_metadata.standard_id = ? and "
			+ "exam_metadata.academic_session_id = ? and standard_id in (select standard_id from standards where institute_id = ?)";

	private static final String GET_CLASS_EXAM_GRAPH_WITH_COURSES_BY_STANDARD = "select exam_metadata.*, class_courses.*, exam_graph_edges.child_exam_id from "
			+ "exam_metadata left join exam_courses_assignment on exam_metadata.exam_id = exam_courses_assignment.exam_id left join "
			+ "class_courses on exam_courses_assignment.course_id = class_courses.course_id left join "
			+ "exam_graph_edges on exam_metadata.exam_id = exam_graph_edges.exam_id where exam_metadata.standard_id = ? and "
			+ "exam_metadata.academic_session_id = ? and exam_metadata.standard_id in (select standard_id from standards where institute_id = ?)";

	private static final String GET_CLASS_MARKS_BY_COURSE_ID = "select students.*, marks_feeding.* from students join student_academic_session_details "
			+ "where students.student_id = student_academic_session_details.student_id and student_academic_session_details.academic_session_id = ? "
			+ "and student_academic_session_details.standard_id = ? left join marks_feeding on students.student_id = marks_feeding.student_id "
			+ "where exam_id = ? and course_id = ? and student_academic_session_details.standard_id in "
			+ "(select standard_id from standards where institute_id = ?)";
	//
	// private final String GET_EXAM_COURSE_MARKS_DETAILS = "select students.*,
	// exam_metadata.*, exam_dimensions.*, exam_dimensions_mapping.*, "
	// + "class_courses.*, marks_feeding.* from exam_metadata join
	// student_academic_session_details on exam_metadata.academic_session_id = "
	// + "student_academic_session_details.academic_session_id and
	// student_academic_session_details.standard_id = ? join students on "
	// + "students.student_id = student_academic_session_details.student_id join
	// exam_dimensions_mapping on "
	// + "exam_metadata.exam_id= exam_dimensions_mapping.exam_id join "
	// + "exam_dimensions on exam_dimensions_mapping.dimension_id=
	// exam_dimensions.dimension_id left join marks_feeding on "
	// + "students.student_id = marks_feeding.student_id and
	// exam_metadata.exam_id= marks_feeding.exam_id and
	// exam_dimensions_mapping.dimension_id = "
	// + "marks_feeding.dimension_id left join class_courses on
	// marks_feeding.course_id = class_courses.course_id and "
	// + "exam_dimensions_mapping.course_type= class_courses.course_type "
	// + "where exam_metadata.exam_id = ? and marks_feeding.course_id = ?";

	private static final String GET_EXAM_COURSE_MARKS_DETAILS = "select * from exam_metadata "
			+ " join student_academic_session_details on exam_metadata.academic_session_id = student_academic_session_details.academic_session_id and student_academic_session_details.standard_id = exam_metadata.standard_id "
			+ " join students on students.student_id = student_academic_session_details.student_id "
			+ " join exam_dimensions_mapping on exam_metadata.exam_id= exam_dimensions_mapping.exam_id "
			+ " join exam_dimensions on exam_dimensions_mapping.dimension_id = exam_dimensions.dimension_id "
			+ " left join exam_courses_assignment on exam_dimensions_mapping.exam_id= exam_courses_assignment.exam_id and exam_dimensions_mapping.dimension_id = exam_courses_assignment.dimension_id "
			+ " join class_courses on exam_courses_assignment.course_id = class_courses.course_id and exam_dimensions_mapping.course_type= class_courses.course_type "
			+ " left join marks_feeding on students.student_id = marks_feeding.student_id and exam_metadata.exam_id = marks_feeding.exam_id and exam_courses_assignment.course_id = marks_feeding.course_id and exam_dimensions_mapping.dimension_id = marks_feeding.dimension_id %s "
			+ " left join examination_grades on examination_grades.grade_id = marks_feeding.grade_obtained "
			+ " inner join academic_session on academic_session.academic_session_id = student_academic_session_details.academic_session_id  "
			+ " inner join standards on standards.standard_id = student_academic_session_details.standard_id "
			+ " left join standard_section_mapping on (standard_section_mapping.standard_id = student_academic_session_details.standard_id and standard_section_mapping.academic_session_id = student_academic_session_details.academic_session_id and standard_section_mapping.section_id = student_academic_session_details.section_id) "
			+ " where exam_metadata.exam_id = ? and student_academic_session_details.session_status in %s %s %s %s ";

	private static final String GET_REPORT_CARD_VARIABLES_BY_REPORT_TYPE = "select * from students "
			+ " inner join student_academic_session_details on student_academic_session_details.student_id = students.student_id  "
			+ " left join report_card_variables on (report_card_variables.student_id = students.student_id  and report_card_variables.academic_session_id = student_academic_session_details.academic_session_id and report_card_variables.report_type = ?) "
			+ " inner join academic_session on academic_session.academic_session_id = student_academic_session_details.academic_session_id  "
			+ " inner join standards on standards.standard_id = student_academic_session_details.standard_id "
			+ " left join standard_section_mapping on (standard_section_mapping.standard_id = student_academic_session_details.standard_id "
			+ " and standard_section_mapping.academic_session_id = student_academic_session_details.academic_session_id and "
			+ " standard_section_mapping.section_id = student_academic_session_details.section_id) "
			+ " where student_academic_session_details.standard_id = ? and students.institute_id = ? and student_academic_session_details.academic_session_id = ? and "
			+ " student_academic_session_details.session_status = 'ENROLLED' %s %s order by students.name";

	// Report structure
	private static final String UPSERT_REPORT_STRUCTURE = "insert into exam_report_structure(institute_id, academic_session_id, report_card_id, standard_id, "
			+ "report_type, report_card_name, structure) values(?, ?, ?, ?, ?, ?, ?) on duplicate key update report_card_name = ?, structure = ?";

	private static final String GET_REPORT_STRUCTURE = "select * from exam_report_structure where institute_id = ? and academic_session_id = ? and "
			+ "standard_id = ? and report_type = ?";

	private static final String DELETE_REPORT_STRUCTURE = " delete from exam_report_structure where institute_id = ? and academic_session_id = ? and "
			+ "standard_id = ? and report_type = ? ";

	private static final String GET_ALL_REPORT_STRUCTURE = "select * from exam_report_structure where institute_id = ? and academic_session_id = ? and "
			+ " report_type = ?";

	private static final String GET_REPORT_STRUCTURE_TYPES = "select * from exam_report_structure where institute_id = ? and academic_session_id = ? and "
			+ "standard_id = ? order by report_card_name";

	private static final String GET_ALL_REPORT_STRUCTURE_TYPES = "select * from exam_report_structure where institute_id = ? and academic_session_id = ? "
			+ " order by report_card_name";

	private static final String UPSERT_GREEN_SHEET_EXAM_STRUCTURE = "insert into green_sheet_exam_structure (institute_id, academic_session_id, "
			+ "standard_id, green_sheet_structure) values(?, ? ,?, ?) on duplicate key update green_sheet_structure = ?";

	private static final String GET_GREEN_SHEET_EXAM_STRUCTURE = "select * from green_sheet_exam_structure where institute_id = ? and "
			+ "academic_session_id = ? and standard_id = ?";

	private final JdbcTemplate jdbcTemplate;
	private final TransactionTemplate transactionTemplate;
	private static final Gson GSON = new Gson();

	private static final String UPSERT_REPORT_CARD_ATTRIBUTES = "insert into report_card_variables(institute_id, "
			+ " academic_session_id, report_type, student_id, attended_days, working_days, remarks, principal_remarks, height, weight, date_of_result_declaration) "
			+ " values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?) on duplicate key update attended_days =?, working_days=?, remarks=?, principal_remarks=?"
			+ " height=?, weight=?, date_of_result_declaration=?";

	private static final String GET_REPORT_CARD_VARIABLES = "select * from report_card_variables where institute_id = ? and academic_session_id = ? and report_type = ?";

	private static final String GET_STANDARD_META_DATA = "select standards_metadata.* from standards_metadata join exam_metadata "
			+ "on exam_metadata.standard_id = standards_metadata.standard_id and exam_metadata.academic_session_id = "
			+ "standards_metadata.academic_session_id where institute_id = ? and exam_id = ?";

	private static final String GET_GRADES_BY_CLASS_AND_COURSE_TYPE = "select * from examination_grades where institute_id = ? and "
			+ "academic_session_id = ? and standard_id = ? and course_type = ? order by grade_value desc";

	private static final String GET_GRADES_STRING = "select * from examination_grades where institute_id = ? and "
			+ "academic_session_id = ?";

	private static final String UPDATE_GRADE_DETAILS = " UPDATE examination_grades set " +
			"grade_name = ?, range_display_name = ?, remarks = ?, credit_score = ? where grade_id = ?";

	private static final String GET_GRADES_BY_CLASS = "select * from examination_grades where institute_id = ? and "
			+ "academic_session_id = ? and standard_id = ? order by grade_value desc";

	private static final String CREATE_EXAM_GRADES = "insert into examination_grades " +
			" (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name, remarks, credit_score) " +
			" values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

	private static final String DELETE_EXAM_GRADES = "DELETE FROM examination_grades where institute_id = ? and "
			+ "academic_session_id = ? ";

	private static final String GET_GRADES_BY_EXAM_AND_COURSE_TYPE = "select examination_grades.* from examination_grades join "
			+ "exam_metadata on exam_metadata.standard_id = examination_grades.standard_id and exam_metadata.academic_session_id = "
			+ "examination_grades.academic_session_id where institute_id = ? and exam_id = ? and course_type = ?  order by grade_value desc ";

	private static final String ADD_DATESHEET = "insert into syllabus_and_datesheet_metadata(institute_id, academic_session_id, "
			+ "datesheet_id, standard_id, section_id, exam_id, exam_start_date, exam_end_date, status, added_by, notes) "
			+ "values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

	private static final String ADD_DATESHEET_DETAILS = "insert into syllabus_and_datesheet_details "
			+ "(datesheet_id, course_id, dimension_id, start_time, end_time, syllabus) "
			+ "values (?, ?, ?, ?, ?, ?)";

	private static final String DELETE_FROM_DATESHEET_DETAILS = "delete from syllabus_and_datesheet_details " +
			"where datesheet_id = ? ";

	private static final String UPDATE_DATESHEET_METADATA = "update syllabus_and_datesheet_metadata set " +
			"exam_start_date = ?, exam_end_date = ?, updated_by = ?, notes = ? where datesheet_id = ? ";

	private final static String DELETE_DATESHEET_METADATA = "delete from syllabus_and_datesheet_metadata " +
			"where datesheet_id = ? ";

	private final static String GET_DATESHEET_METADATA = "select * from syllabus_and_datesheet_metadata " +
			"inner join standards on standards.standard_id = syllabus_and_datesheet_metadata.standard_id " +
			"left join standard_section_mapping on standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = ? " +
			"left join exam_metadata on exam_metadata.standard_id = syllabus_and_datesheet_metadata.standard_id and exam_metadata.exam_id = syllabus_and_datesheet_metadata.exam_id " +
			"where syllabus_and_datesheet_metadata.standard_id = ? and syllabus_and_datesheet_metadata.academic_session_id = ? and syllabus_and_datesheet_metadata.institute_id = ?  ";

	private final static String GET_DATESHEET_DETAILS = "select * from syllabus_and_datesheet_metadata " +
			"inner join syllabus_and_datesheet_details on syllabus_and_datesheet_metadata.datesheet_id = syllabus_and_datesheet_details.datesheet_id " +
			"inner join standards on standards.standard_id = syllabus_and_datesheet_metadata.standard_id " +
			"left join standard_section_mapping on standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = ? " +
			"inner join exam_metadata on exam_metadata.standard_id = syllabus_and_datesheet_metadata.standard_id  and exam_metadata.exam_id = syllabus_and_datesheet_metadata.exam_id " +
			"inner join exam_dimensions on exam_dimensions.dimension_id =  syllabus_and_datesheet_details.dimension_id " +
			"inner join class_courses on class_courses.course_id = syllabus_and_datesheet_details.course_id " +
			"where syllabus_and_datesheet_metadata.institute_id = ? and  syllabus_and_datesheet_metadata.academic_session_id = ? " +
			" and  syllabus_and_datesheet_metadata.standard_id = ? ";

	private final static String DATESHEET_ID_CLAUSE = " and syllabus_and_datesheet_metadata.datesheet_id = ? ";

	private final static String EXAM_ID_CLAUSE = " and syllabus_and_datesheet_metadata.exam_id = ? ";

	private static final String UPDATE_EXAM_COURSES_STATUS = "update exam_courses_assignment set status = ? " +
			" where exam_id = ? ";

	private static final String ADD_PERSONALITY_TRAITS = " insert into personality_traits_details(institute_id, academic_session_id, personality_trait_id, personality_trait_name, standard_id, personality_trait_sequence) " +
			" values(?, ?, ?, ?, ?, ?) ";
	private static final String UPDATE_PERSONALITY_TRAITS = " update personality_traits_details set personality_trait_name = ?, personality_trait_sequence = ? " +
			" where personality_trait_id = ? ";
	private static final String DELETE_PERSONALITY_TRAITS = " delete from personality_traits_details " +
			" where personality_traits_details.institute_id = ? and personality_traits_details.academic_session_id = ? " +
			" and personality_traits_details.standard_id = ? and personality_traits_details.personality_trait_id = ? ";
	private static final String GET_INSTITUTE_PERSONALITY_TRAITS = " select * from personality_traits_details " +
			" where personality_traits_details.institute_id = ? and personality_traits_details.academic_session_id = ? ";
	private static final String STANDARD_WHERE_CLAUSE = " and personality_traits_details.standard_id = ? ";
	private static final String GET_PERSONALITY_TRAITS_FOR_MULTIPLE_STANDARDS = " select * from personality_traits_details " +
			" where personality_traits_details.institute_id = ? and personality_traits_details.academic_session_id = ? " +
			" and personality_traits_details.standard_id IN (%s) ";
	private static final String GET_STUDENT_PERSONALITY_TRAIT_USAGE_DETAILS = " select ptd.personality_trait_id, ptd.personality_trait_name, " +
			" ptd.standard_id, s.standard_name, sptm.report_type " +
			" from personality_traits_details ptd " +
			" inner join personality_traits_student_mapping sptm on ptd.personality_trait_id = sptm.personality_trait_id " +
			" inner join standards s on ptd.standard_id = s.standard_id " +
			" where ptd.institute_id = ? and ptd.academic_session_id = ? " +
			" and ptd.personality_trait_id IN (%s) ";
	private static final String DELETE_PERSONALITY_TRAITS_BY_IDS = " delete from personality_traits_details " +
			" where personality_traits_details.institute_id = ? and personality_traits_details.personality_trait_id IN (%s) ";

	private static final String GET_STUDENT_PERSONALITY_TRAIT_DETAILS = "select * from students "
			+ " inner join student_academic_session_details on student_academic_session_details.student_id = students.student_id  "
			+ " left join personality_traits_student_mapping on (personality_traits_student_mapping.student_id = students.student_id and personality_traits_student_mapping.academic_session_id = student_academic_session_details.academic_session_id and personality_traits_student_mapping.report_type = ?) "
			+ " left join personality_traits_details on personality_traits_details.personality_trait_id = personality_traits_student_mapping.personality_trait_id "
			+ " inner join academic_session on academic_session.academic_session_id = student_academic_session_details.academic_session_id  "
			+ " inner join standards on standards.standard_id = student_academic_session_details.standard_id "
			+ " left join standard_section_mapping on (standard_section_mapping.standard_id = student_academic_session_details.standard_id "
			+ " and standard_section_mapping.academic_session_id = student_academic_session_details.academic_session_id and "
			+ " standard_section_mapping.section_id = student_academic_session_details.section_id) "
			+ " where student_academic_session_details.standard_id = ? and students.institute_id = ? and student_academic_session_details.academic_session_id = ? and "
			+ " student_academic_session_details.session_status = 'ENROLLED' %s %s order by students.name";

	private static final String UPSERT_REPORT_CARD_PERSONALITY_TRAITS = " insert into personality_traits_student_mapping(institute_id, "
			+ " academic_session_id, personality_trait_id, report_type, student_id, remarks) "
			+ " values (?, ?, ?, ?, ?, ?) on duplicate key update remarks = ? ";

	private static final String ADD_STANDARD_GRADING = " insert into standards_metadata (institute_id, academic_session_id, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled) values (?, ?, ?, ?, ?) "
			+ "on duplicate key update coscholastic_grade_enabled = ?, scholastic_grade_enabled = ? ";

	private static final String DELETE_EXAMINATION_GRADES_STANDARD_WISE = " delete from examination_grades where academic_session_id = ? and course_type in %s and standard_id in %s ";

	private static final String GET_MARKS_BY_STANDARD_ID_ONLY_GRADES = " select * from marks_feeding "
			+ " inner join examination_grades on examination_grades.grade_id = marks_feeding.grade_obtained "
			+ " where academic_session_id = ? and examination_grades.standard_id in %s ";

	private static final String GET_MARKS_BY_STANDARD_ID = " select marks_feeding.* from marks_feeding "
			+ " join exam_metadata ON marks_feeding.exam_id = exam_metadata.exam_id "
			+ " where exam_metadata.academic_session_id = ? and exam_metadata.standard_id in %s ";

	private static final String GET_STUDENT_REPORT_CARD_DETAILS = " select * from students "
			+ " inner join student_academic_session_details on student_academic_session_details.student_id = students.student_id and student_academic_session_details.academic_session_id = ? "
			+ " inner join standards on standards.standard_id = student_academic_session_details.standard_id "
			+ " left join standard_section_mapping on standard_section_mapping.section_id = student_academic_session_details.section_id "
			+ " inner join academic_session on academic_session.academic_session_id = student_academic_session_details.academic_session_id "
			+ " inner join exam_report_structure on exam_report_structure.academic_session_id = student_academic_session_details.academic_session_id and exam_report_structure.standard_id = student_academic_session_details.standard_id "
			+ " left join student_report_card_mapping on student_report_card_mapping.student_id = student_academic_session_details.student_id and student_report_card_mapping.academic_session_id = student_academic_session_details.academic_session_id and exam_report_structure.report_card_id = student_report_card_mapping.report_card_id "
			+ " where students.institute_id = ? and student_academic_session_details.academic_session_id = ? "
			+ " and student_academic_session_details.session_status = 'ENROLLED' %s %s %s %s %s order by students.name";

	private static final String STANDARD_ID_CLAUSE = " and student_academic_session_details.standard_id = ? ";
	private static final String SECTION_CLAUSE = " and student_academic_session_details.section_id in ";
	private static final String STUDENT_CLAUSE = " and students.student_id = ? ";
	private static final String REPORT_CARD_ID_CLAUSE = " and exam_report_structure.report_card_id = ? ";
	private static final String REPORT_CARD_STATUS_CLAUSE = " and student_report_card_mapping.status = 'PUBLISHED' ";

	private static final String UPSERT_STUDENT_REPORT_CARD_MAPPING = " insert into student_report_card_mapping"
			+ " (institute_id, academic_session_id, report_card_id, student_id, status) "
			+ " values (?, ?, ?, ?, ?) on duplicate key update status = ? ";

	private static final String BULK_UPSERT_STANDARDS_METADATA = " insert into standards_metadata "
			+ " (institute_id, academic_session_id, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled, round_exam_report_marks) "
			+ " values (?, ?, ?, ?, ?, ?) on duplicate key update "
			+ " coscholastic_grade_enabled = VALUES(coscholastic_grade_enabled), "
			+ " scholastic_grade_enabled = VALUES(scholastic_grade_enabled), "
			+ " round_exam_report_marks = VALUES(round_exam_report_marks) ";


	public ExaminationDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate) {
		this.jdbcTemplate = jdbcTemplate;
		this.transactionTemplate = transactionTemplate;
	}

	public boolean createExamDimension(int instituteId, ExamDimension dimensionPayload) {
		try {
			return jdbcTemplate.update(ADD_EXAM_DIMENSION, instituteId, dimensionPayload.getDimensionName(),
					dimensionPayload.getDimensionType().name(), dimensionPayload.getExamEvaluationType().name(),
					dimensionPayload.isTotal()) == 1;
		} catch (final Exception e) {
			logger.error("Error while creating exam diminsion", e);
		}
		return false;
	}

	public List<ExamMetaData> getExamMetadata(int instituteId) {
		try {
			return jdbcTemplate.query(GET_EXAM_METADATA_BY_INSTITUTE, new Object[]{instituteId},
					EXAM_METADATA_MAPPER);
		} catch (final Exception e) {
			logger.error("Error while getting exam metadata for instituteId {} ", instituteId, e);
		}
		return null;
	}

	public List<ExamDimension> getExamDimensions(int instituteId) {
		try {
			return jdbcTemplate.query(GET_EXAM_DIMENSION_BY_INSTITUTE, new Object[]{instituteId},
					EXAM_DIMENSION_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Error while getting exam diminsion", e);
		}
		return null;
	}

	public List<ExamNodeData> getExamGraphNodesByStandard(UUID standardId, int academicSessionId, int instituteId,
														  boolean includeCourses) {
		try {
			if (includeCourses) {
				return ExamNodeEdgeWithCoursesRowMapper
						.getExamNodeData(jdbcTemplate.query(GET_CLASS_EXAM_GRAPH_WITH_COURSES_BY_STANDARD,
								new Object[]{standardId.toString(), academicSessionId, instituteId},
								EXAM_NODE_EDGE_WITH_COURSES_ROW_MAPPER));
			}
			return ExamNodeEdgeRowMapper.getExamNodeData(jdbcTemplate.query(GET_CLASS_EXAM_GRAPH_BY_STANDARD,
					new Object[]{standardId.toString(), academicSessionId, instituteId}, EXAM_NODE_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Error while getting exam nodes", e);
		}
		return null;
	}

	public List<ExamNodeData> getExamGraphNodesByExam(UUID examId, int instituteId, boolean includeCourses) {
		try {
			if (includeCourses) {
				return ExamNodeEdgeWithCoursesRowMapper
						.getExamNodeData(jdbcTemplate.query(GET_CLASS_EXAM_GRAPH_WITH_COURSES_BY_EXAM,
								new Object[]{examId.toString(), examId.toString(), instituteId},
								EXAM_NODE_EDGE_WITH_COURSES_ROW_MAPPER));
			}
			return ExamNodeEdgeRowMapper.getExamNodeData(jdbcTemplate.query(GET_CLASS_EXAM_GRAPH_BY_EXAM,
					new Object[]{examId.toString(), examId.toString(), instituteId}, EXAM_NODE_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Error while getting exam nodes", e);
		}
		return null;
	}

	/**
	 * Always use this method from manager class instead of calling it from dao
	 * directly as filtering is required for dimensions based on the configurations
	 * in db
	 */
	public ExamDetails getExamDetails(int instituteId, UUID examId) {
		try {
			return ExamDetailsRowMapper.getExamDetails(jdbcTemplate.query(GET_EXAM_DETAILS,
					new Object[]{examId.toString(), instituteId}, EXAM_DETAILS_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Error while getting exam details id {}, institute {}", examId, instituteId, e);
		}
		return null;
	}

	/**
	 * Always use this method from manager class instead of calling it from dao
	 * directly as filtering is required for dimensions based on the configurations
	 * in db
	 */
	public List<ExamDetails> getExamDetails(int instituteId, int academicSessionId, UUID standardId) {
		try {
			return ExamDetailsRowMapper.getClassSessionExamDetails(jdbcTemplate.query(GET_CLASS_SESSION_EXAM_DETAILS,
					new Object[]{standardId.toString(), academicSessionId, instituteId}, EXAM_DETAILS_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Error while getting exam details academicSessionId {}, standardId {}, institute {}",
					academicSessionId, standardId, instituteId, e);
		}
		return null;
	}

	/**
	 * Always use this method from manager class instead of calling it from dao
	 * directly as filtering is required for dimensions based on the configurations
	 * in db
	 */
	public List<ExamDetails> getAllExamDetailsInStandardByExamId(int instituteId, UUID examId) {
		try {
			return ExamDetailsRowMapper.getClassSessionExamDetails(jdbcTemplate.query(
					GET_CLASS_SESSION_EXAM_DETAILS_BY_EXAM_ID,
					new Object[]{examId.toString(), examId.toString(), instituteId}, EXAM_DETAILS_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Error while getting exam details for examId {}, institute {}", examId, instituteId, e);
		}
		return null;
	}

	public UUID createExam(ExamCreationPayload examCreationPayload) {
		try {

			logger.info("{}", examCreationPayload);
			final UUID examId = transactionTemplate.execute(new TransactionCallback<UUID>() {

				@Override
				public UUID doInTransaction(TransactionStatus status) {
					final UUID examId = addExamMetaData(examCreationPayload);
					if (examId == null) {
						throw new EmbrateRunTimeException("Unable to add exam meta data");
					}
					if (!addExamDimensionMapping(examCreationPayload, examId)) {
						throw new EmbrateRunTimeException("Unable to add exam dimesnion mappings");
					}
					if ((examCreationPayload.getParentExamId() != null)
							&& !addExamGraphEdge(examId, (examCreationPayload.getParentExamId()))) {
						throw new EmbrateRunTimeException("Unable to add exam graph edges");
					}

					return examId;

				}
			});
			return examId;
		} catch (final Exception e) {
			logger.error("Error while creating exam ", e);
		}
		return null;
	}

	public Boolean updateExamMetaData(ExamUpdatePayload examUpdatePayload) {
		try {
			Timestamp attendanceStartDate = examUpdatePayload.getAttendanceStartDate() == null ? null
					: new Timestamp(examUpdatePayload.getAttendanceStartDate() * 1000L);
			Timestamp attendanceEndDate = examUpdatePayload.getAttendanceEndDate() == null ? null
					: new Timestamp(examUpdatePayload.getAttendanceEndDate() * 1000L);
			Timestamp examStartDate = examUpdatePayload.getExamStartDate() == null ? null
					: new Timestamp(examUpdatePayload.getExamStartDate() * 1000L);
			Timestamp dateOfResultDeclaration = examUpdatePayload.getDateOfResultDeclaration() == null ? null
					: new Timestamp(examUpdatePayload.getDateOfResultDeclaration() * 1000L);

			return jdbcTemplate.update(UPDATE_EXAM_META_DATA, examUpdatePayload.getExamName(),
					examUpdatePayload.getOperation().name(), attendanceStartDate, attendanceEndDate,
					examStartDate, dateOfResultDeclaration,
					examUpdatePayload.getScholasticExamMarksDisplayType() == null ? null :
							examUpdatePayload.getScholasticExamMarksDisplayType().name(),
					examUpdatePayload.getCoScholasticExamMarksDisplayType() == null ? null :
							examUpdatePayload.getCoScholasticExamMarksDisplayType().name(), examUpdatePayload.getExamId().toString()) == 1;

		} catch (final Exception e) {
			logger.error("Error while updating exam {}", examUpdatePayload.getExamId(), e);
		}
		return false;

	}

	private Boolean updateParentExamIdInMetaData(UUID childExamId, List<UUID> parentExamIds) {
		try {
			return jdbcTemplate.update(UPDATE_PARENT_EXAM_IDS_IN_META_DATA,
					GSON.toJson(parentExamIds), childExamId.toString()) == 1;

		} catch (final Exception e) {
			logger.error("Error while updating parent exam id for childExamId {}, parentExamIds {}", childExamId, parentExamIds, e);
		}
		return false;

	}

	public boolean deleteExam(int instituteId, UUID examId) {
		final boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
			@Override
			public Boolean doInTransaction(TransactionStatus status) {
				return deleteExamNonAtomic(instituteId, examId);
			}
		});
		return success;
	}

	// Must be used within transaction only
	public Boolean deleteExamNonAtomic(int instituteId, UUID examId) {
		if ((checkParentExam(instituteId, examId, false).size() > 0)) {
			throw new EmbrateRunTimeException("ExamId is a Parent Exam. Please delete it's child first.");
		}

		if (checkSystemExam(instituteId, examId)) {
			throw new EmbrateRunTimeException("This is a system exam. It can't be deleted.");
		}

		Boolean result = true;
		result &= deleteMarksByExamID(instituteId, examId);
		result &= deleteExamCoursesAssignmentByExamID(instituteId, examId);
		result &= deleteExamDimensionMapping(instituteId, examId);
		result &= deleteExamGraphEdgesByExamID(instituteId, examId);
		result &= deleteExamByExamID(instituteId, examId);

		return result;
	}

	private boolean checkSystemExam(int instituteId, UUID examId) {
		final ExamMetaData examMetaData = jdbcTemplate.queryForObject(GET_EXAM_METADATA,
				new Object[]{examId.toString()}, EXAM_METADATA_MAPPER);

		if ((examMetaData != null) && (ExamType.SYSTEM == examMetaData.getExamType())) {
			return true;
		}
		return false;
	}

	/**
	 * below function is assuming that a standard will have only one system exam
	 * @param instituteId
	 * @param academicSessionId
	 * @param standardId
	 * @return
	 */
	public ExamMetaData getSystemExamMetadata(int instituteId, int academicSessionId, UUID standardId) {
		try {
			return jdbcTemplate.queryForObject(GET_SYSTEM_EXAM_METADATA, new Object[]{ academicSessionId, standardId.toString() }, EXAM_METADATA_MAPPER);
		} catch (final Exception e) {
			e.printStackTrace();
			logger.error("Error while getting system exam metadata", e);
		}
		return null;
	}

	public List<ExamCoursesAssigmentRowDetails> getExamCoursesAssignmentByExamId(UUID examId, int instituteId) {
		try {
			return jdbcTemplate.query(GET_EXAM_COURSES_ASSIGMENT_BY_EXAM_ID, new Object[]{examId.toString()},
					EXAM_COURSES_ASSIGMENT_MAPPER);
		} catch (final Exception e) {
			logger.error("Error while getting exam courses", e);
		}
		return null;
	}

	public List<ExamNodeData> checkParentExam(int instituteId, UUID examId, Boolean checkIfChild) {
		try {
			if (checkIfChild) {
				return ExamNodeEdgeRowMapper.getExamNodeData(
						jdbcTemplate.query(CHECK_CHILD_EXAM, new Object[]{examId.toString()}, EXAM_NODE_ROW_MAPPER));
			} else {
				return ExamNodeEdgeRowMapper.getExamNodeData(jdbcTemplate.query(CHECK_PARENT_EXAM,
						new Object[]{examId.toString()}, EXAM_NODE_ROW_MAPPER));
			}
		} catch (final Exception e) {
			logger.error("Error while checking parent exam", e);
		}
		return null;
	}

	public Boolean deleteExamGraphEdgesByExamID(int instituteId, UUID examId) {
		try {
			return jdbcTemplate.update(DELETE_EXAM_GRAPH_EDGES_BY_EXAM_ID, examId.toString()) > 0;
		} catch (final Exception e) {
			logger.error("Error while deleting exam graph", e);
		}
		return false;
	}

	/**
	 * This method should be called only after proper validation of marks submitted etc.
	 * Ow it can lead to orphan marks data
	 */
	public boolean updateParentMapping(int instituteId, UUID childExamId, List<UUID> newParentIds) {
		try {
			return transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus transactionStatus) {
					Boolean success = deleteExamGraphEdgesByExamID(instituteId, childExamId);
					if (success == null || !success) {
						logger.error("Unable to delete the existing child parent mapping for instituteId {}, childExamId {}", instituteId, childExamId);
						throw new EmbrateRunTimeException("Unable to delete the existing child parent mapping");
					}
					if (!addExamGraphEdge(childExamId, newParentIds)) {
						logger.error("Unable to add the new child parent mapping for instituteId {}, childExamId {}, newParentIds {}", instituteId, childExamId, newParentIds);
						throw new EmbrateRunTimeException("Unable to add the new child parent mapping");
					}
					if (!updateParentExamIdInMetaData(childExamId, newParentIds)) {
						logger.error("Unable to add the new parent for instituteId {}, childExamId {}, newParentIds {}", instituteId, childExamId, newParentIds);
						throw new EmbrateRunTimeException("Unable to add the parent mapping");
					}
					return true;
				}
			});
		} catch (Exception e) {
			logger.error("Exception while updating the parent for instituteId {}, childExamId {}, newParentIds {}", instituteId, childExamId, newParentIds, e);
		}
		return false;
	}

	public List<ExamDimensionsRowDetails> getExamDimensionsMappingByExamID(int instituteId, UUID examId) {
		try {
			return jdbcTemplate.query(GET_EXAM_DIMENSIONS_MAPPING_BY_EXAM_ID, new Object[]{examId.toString()},
					EXAM_DIMENSION_MAPPING_MAPPER);

		} catch (final Exception e) {
			logger.error("Error while getting  exam dimensions", e);
		}
		return null;
	}

//	GET_EXAM_BY_CLASS_ID

	public Boolean deleteExamCoursesAssignmentByExamID(int instituteId, UUID examId) {
		try {
			jdbcTemplate.update(DELETE_EXAM_COURSES_ASSIGMENT_BY_EXAM_ID, examId.toString());
			return true;
		} catch (final Exception e) {
			logger.error("Error while deleting  exam courses", e);
		}
		return false;
	}

	public boolean deleteExamByExamID(int instituteId, UUID examId) {
		try {
			return jdbcTemplate.update(DELETE_EXAM, examId.toString()) == 1;
		} catch (final Exception e) {
			logger.error("Error while deleting Exam", e);
		}
		return false;
	}

	public Boolean deleteMarksByExamID(int institudeId, UUID examID) {
		try {
			jdbcTemplate.update(DELETE_FEED_MARKS_BY_EXAM_ID, examID.toString());
			return true;
		} catch (final Exception e) {
			logger.error("Error while deleting marks by exam {}", examID, e);
		}
		return false;
	}

	public boolean addCoursesInExam(UUID examId, CourseType courseType, List<ExamCoursesPayload> examCoursesPayloads,
									Boolean update) {
		if (!update) {
			return addCoursesInExam(examId, examCoursesPayloads);
		} else {
			return updateCoursesInExam(examId, courseType, examCoursesPayloads);
		}
	}

	public boolean addCoursesInExam(UUID examId, List<ExamCoursesPayload> examCoursesPayloads) {
		try {
			final Boolean status = transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus status) {
					final List<Object[]> examCoursesBatchArgs = new ArrayList<>();
					for (final ExamCoursesPayload examCreationPayload : examCoursesPayloads) {
						final UUID courseId = examCreationPayload.getCourseId();
						for (final ExamDimensionValuesPayload dimensionValuesPayload : examCreationPayload
								.getDimensionValuesPayloads()) {
							examCoursesBatchArgs.add(new Object[]{examId.toString(), courseId.toString(),
									dimensionValuesPayload.getDimensionId(), dimensionValuesPayload.getMaxMarks(),
									dimensionValuesPayload.getMinMarks(), dimensionValuesPayload.getMaxGrade(),
									dimensionValuesPayload.getMinGrade(), ExamCoursePublishedStatus.UNPUBLISHED.name()});
						}
					}
					final int[] rows = jdbcTemplate.batchUpdate(ADD_EXAM_COURSES, examCoursesBatchArgs);
					return rows.length == examCoursesBatchArgs.size();
				}
			});
			return status.booleanValue();
		} catch (final Exception e) {
			logger.error("Error while adding courses in exam {}", examId, e);
		}
		return false;
	}

	// TODO: Need to restrict the course addition and update to course type
	// only.
	public Boolean updateCoursesInExam(UUID examId, CourseType courseType,
									   List<ExamCoursesPayload> examCoursesPayloads) {

		// courseId of all the courses present in DB
		final ExamCoursesData examCoursesData = getCoursesInExam(examId, courseType);
		final List<ExamCourse> existingExamCourses = examCoursesData == null ? new ArrayList<>()
				: examCoursesData.getExamCourses();
		final Map<UUID, ExamCourse> existingExamCoursesMap = new HashMap<>();
		final Set<UUID> existingCourseIds = new HashSet<>();
		for (final ExamCourse examCourse : existingExamCourses) {
			existingExamCoursesMap.put(examCourse.getCourse().getCourseId(), examCourse);
			existingCourseIds.add(examCourse.getCourse().getCourseId());
		}

		final Set<UUID> examCoursesNewIds = new HashSet<>();

		// Get New Courses from User
		final Map<UUID, ExamCoursesPayload> examCoursesPayloadMap = new HashMap<>();
		for (final ExamCoursesPayload examCoursesPayload : examCoursesPayloads) {
			examCoursesNewIds.add(examCoursesPayload.getCourseId());
			examCoursesPayloadMap.put(examCoursesPayload.getCourseId(), examCoursesPayload);
		}

		// Courses to be Inserted
		final Set<UUID> insertion = new HashSet<>(examCoursesNewIds);
		insertion.removeAll(existingCourseIds);
		final List<ExamCoursesPayload> newExamCoursesPayload = new ArrayList<>();
		final List<ExamCoursesPayload> updateExamCoursesPayload = new ArrayList<>();
		for (final UUID insertionCourseId : insertion) {
			newExamCoursesPayload.add(examCoursesPayloadMap.get(insertionCourseId));
		}

		// Courses to be Updated
		final Set<UUID> updation = new HashSet<>(examCoursesNewIds);
		updation.retainAll(existingCourseIds);
		for (final UUID updateCourseId : updation) {
			final ExamCourse examCourse = existingExamCoursesMap.get(updateCourseId);
			final Set<Integer> existingDimensions = new HashSet<>();
			for (final ExamDimensionValues examDimensionValues : examCourse.getExamDimensionValues()) {
				existingDimensions.add(examDimensionValues.getExamDimension().getDimensionId());
			}
			final ExamCoursesPayload examCoursesPayload = examCoursesPayloadMap.get(updateCourseId);
			final List<ExamDimensionValuesPayload> updateDimensionValuesPayloads = new ArrayList<>();
			final List<ExamDimensionValuesPayload> newDimensionValuesPayloads = new ArrayList<>();
			for (final ExamDimensionValuesPayload examDimensionValuesPayload : examCoursesPayload
					.getDimensionValuesPayloads()) {
				final Integer dimensionId = examDimensionValuesPayload.getDimensionId();
				if (existingDimensions.contains(dimensionId)) {
					updateDimensionValuesPayloads.add(examDimensionValuesPayload);
				} else {
					newDimensionValuesPayloads.add(examDimensionValuesPayload);
				}
			}
			if (!updateDimensionValuesPayloads.isEmpty()) {
				updateExamCoursesPayload.add(new ExamCoursesPayload(updateCourseId, updateDimensionValuesPayloads));
			}
			if (!newDimensionValuesPayloads.isEmpty()) {
				newExamCoursesPayload.add(new ExamCoursesPayload(updateCourseId, newDimensionValuesPayloads));
			}
		}

		// Courses to be Deleted
		final Set<UUID> deletion = new HashSet<>(existingCourseIds);
		deletion.removeAll(examCoursesNewIds);
		// TODO: This needs to be fixed as internal operations are anyways being
		// done in transactions. So this transaction wont roll back
		final boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
			@Override
			public Boolean doInTransaction(TransactionStatus status) {

				if (!CollectionUtils.isEmpty(deletion) && !deleteExamCourses(examId, deletion)) {
					throw new EmbrateRunTimeException("Unable to delete courses");
				}

				if (!CollectionUtils.isEmpty(newExamCoursesPayload)
						&& !addCoursesInExam(examId, newExamCoursesPayload)) {
					throw new EmbrateRunTimeException("Unable to add new courses");
				}

				if (!CollectionUtils.isEmpty(updateExamCoursesPayload)
						&& !updateCourseDetail(examId, updateExamCoursesPayload)) {
					throw new EmbrateRunTimeException("Unable to update existing courses");
				}
				return true;
			}
		});
		return success;
	}

	public boolean deleteExamCourses(UUID examId, Set<UUID> courseIds) {
		try {
			if (CollectionUtils.isEmpty(courseIds)) {
				return true;
			}
			final List<Object> args = new ArrayList<>();
			args.add(examId.toString());
			final StringBuilder inParams = new StringBuilder();
			inParams.append("(");
			boolean first = true;
			for (final UUID courseId : courseIds) {
				args.add(courseId.toString());
				if (first) {
					inParams.append(" ?");
					first = false;
					continue;
				}
				inParams.append(", ?");
			}
			inParams.append(")");
			return jdbcTemplate.update(String.format(DELETE_EXAM_COURSES, inParams.toString()), args.toArray()) > 0;
		} catch (final Exception e) {
			logger.error("Error while deleting exam courses in exam {} ", examId, e);
		}
		return false;
	}

	public boolean updateCourseDetail(UUID examId, List<ExamCoursesPayload> examCoursesPayloads) {
		try {
			final List<Object[]> args = new ArrayList<>();
			for (final ExamCoursesPayload examCoursesPayload : examCoursesPayloads) {
				final List<ExamDimensionValuesPayload> examDimensionValuesPayload = examCoursesPayload
						.getDimensionValuesPayloads();
				for (final ExamDimensionValuesPayload examDimensionValues : examDimensionValuesPayload) {
					args.add(new Object[]{examDimensionValues.getMaxMarks(), examDimensionValues.getMinMarks(),
							examDimensionValues.getMaxGrade(), examDimensionValues.getMinGrade(), examId.toString(),
							examCoursesPayload.getCourseId().toString(), examDimensionValues.getDimensionId()});
				}
			}
			return jdbcTemplate.batchUpdate(UPDATE_EXAM_COURSES, args).length == args.size();
		} catch (final Exception e) {
			logger.error("Error while updating courses in exam {}", examId, e);
		}
		return false;
	}

	public ExamCoursesData getCoursesInExam(UUID examId, CourseType courseType) {
		try {
			final Object[] args = {examId.toString(), courseType.name()};
			return ExamCourseRowMapper.getExamCoursesDataForSingleExam(
					jdbcTemplate.query(GET_EXAM_COURSES_BY_TYPE, args, EXAM_COURSE_ROW_MAPPER));
		} catch (final DataAccessException dataAccessException) {
			dataAccessException.printStackTrace();
		} catch (final Exception e) {
			logger.error("Error while getting courses in exam {} of  CourseType {}", examId, courseType, e);
		}

		return null;
	}

	public List<ExamCoursesData> getExamCourses(int academicSessionId, UUID standardId) {
		try {
			final Object[] args = {standardId.toString(), academicSessionId};
			return ExamCourseRowMapper.getExamCoursesDataForMultipleExams(
					jdbcTemplate.query(GET_ALL_EXAM_COURSES_FOR_STANDARD, args, EXAM_COURSE_ROW_MAPPER));
		} catch (final DataAccessException dataAccessException) {
			dataAccessException.printStackTrace();
		} catch (final Exception e) {
			logger.error("Error while getting exam courses standardId {}, academicSessionId {}", standardId,
					academicSessionId, e);
		}

		return null;
	}

	private UUID addExamMetaData(ExamCreationPayload examCreationPayload) {
		try {
			final UUID examId = UUID.randomUUID();
			Timestamp attendanceStartDate = examCreationPayload.getAttendanceStartDate() == null ? null
					: new Timestamp(examCreationPayload.getAttendanceStartDate() * 1000L);
			Timestamp attendanceEndDate = examCreationPayload.getAttendanceEndDate() == null ? null
					: new Timestamp(examCreationPayload.getAttendanceEndDate() * 1000L);
			Timestamp examStartDate = examCreationPayload.getExamStartDate() == null ? null
					: new Timestamp(examCreationPayload.getExamStartDate() * 1000L);
			Timestamp dateOfResultDeclaration = examCreationPayload.getDateOfResultDeclaration() == null ? null
					: new Timestamp(examCreationPayload.getDateOfResultDeclaration() * 1000L);

			String parentExamIdsStr = examCreationPayload.getParentExamId() == null ? null
					: GSON.toJson(examCreationPayload.getParentExamId());

			final boolean success = jdbcTemplate.update(ADD_EXAM_METADATA, examId.toString(),
					examCreationPayload.getExamName(), examCreationPayload.getExamType().name(),
					examCreationPayload.getStandardId().toString(), examCreationPayload.getAcademicSessionId(),
					parentExamIdsStr,
					examCreationPayload.getOperation().name(), attendanceStartDate,
					attendanceEndDate, examStartDate, dateOfResultDeclaration,
					examCreationPayload.getScholasticExamMarksDisplayType() == null ? null :
							examCreationPayload.getScholasticExamMarksDisplayType().name(),
					examCreationPayload.getCoScholasticExamMarksDisplayType() == null ? null :
							examCreationPayload.getCoScholasticExamMarksDisplayType().name()) == 1;
			return success ? examId : null;
		} catch (final Exception e) {
			logger.error("Error while adding exam meta data, {}", examCreationPayload, e);
		}
		return null;
	}

	private boolean addExamDimensionMapping(ExamCreationPayload examCreationPayload, UUID examId) {
		try {
			final List<Object[]> dimensionBatchArgs = new ArrayList<>();
			for (final Entry<CourseType, List<ExamDimensionValuesPayload>> entry : examCreationPayload
					.getCourseTypeDimensions().entrySet()) {
				final CourseType courseType = entry.getKey();
				if (CollectionUtils.isEmpty(entry.getValue())) {
					continue;
				}
				for (final ExamDimensionValuesPayload dimensionValuesPayload : entry.getValue()) {
					dimensionBatchArgs.add(new Object[]{examId.toString(), dimensionValuesPayload.getDimensionId(),
							courseType.name(), dimensionValuesPayload.getMaxMarks(),
							dimensionValuesPayload.getMinMarks(), dimensionValuesPayload.getMaxGrade(),
							dimensionValuesPayload.getMinGrade()});
				}

			}
			final int[] rows = jdbcTemplate.batchUpdate(ADD_EXAM_DIMENSION_MAPPINGS, dimensionBatchArgs);
			return rows.length == dimensionBatchArgs.size();
		} catch (final Exception e) {
			logger.error("Error while adding exam dimension mapping exam {}", examId, e);
		}
		return false;
	}

	private boolean addExamGraphEdge(UUID examId, List<UUID> newParentIds) {
		if ((examId == null) || (CollectionUtils.isEmpty(newParentIds))) {
			return false;
		}
		try {
			List<Object []> args = new ArrayList<>();
			for(UUID parentExamId : newParentIds){
				args.add(new Object[]{parentExamId.toString(), examId.toString()});
			}
			return jdbcTemplate.batchUpdate(ADD_EXAM_GRAPH_EDGE, args).length == newParentIds.size();
		} catch (final Exception e) {
			logger.error("Error while adding exam graph edges for newParentIds {}, exam {}", newParentIds, examId, e);
		}
		return false;
	}

	public boolean updateExamDimension(int instituteId, ExamDimension dimensionPayload) {
		try {
			return jdbcTemplate.update(UPDATE_EXAM_DIMENSION, dimensionPayload.getDimensionName(),
					dimensionPayload.getDimensionType().name(), dimensionPayload.getExamEvaluationType().name(),
					dimensionPayload.isTotal(), instituteId, dimensionPayload.getDimensionId()) == 1;
		} catch (final Exception e) {
			logger.error("Error while deleting exam dimension for institute {}", instituteId, e);
		}
		return false;
	}

	public boolean deleteExamDimension(int instituteId, int dimensionId) {
		try {
			return jdbcTemplate.update(DELETE_EXAM_DIMENSION, instituteId, dimensionId) == 1;
		} catch (final Exception e) {
			logger.error("Error while deleting exam dimension for institute {}", instituteId, e);
		}
		return false;
	}

	public boolean addBulkStandardGrading(int instituteId, int academicSessionId, List<UUID> standardIdsList, boolean isScholasticGradingEnabled, boolean isCoscholasticGradingEnabled) {
		try {
			final List<Object[]> batchInsertArgs = new ArrayList<>();
			int count = 0;
			for (UUID standardId : standardIdsList) {
				final List<Object> args = new ArrayList<>();
				args.add(instituteId);
				args.add(academicSessionId);
				args.add(standardId.toString());
				args.add(isCoscholasticGradingEnabled);
				args.add(isScholasticGradingEnabled);
				args.add(isCoscholasticGradingEnabled);
				args.add(isScholasticGradingEnabled);
				count++;
				batchInsertArgs.add(args.toArray());
			}

			final int[] rows = jdbcTemplate.batchUpdate(ADD_STANDARD_GRADING, batchInsertArgs);
			if (rows.length != count) {
				return false;

			}
			for (final int rowCount : rows) {
				if (rowCount != 1) {
					return false;
				}
			}
			return true;
		} catch (final Exception e) {
			logger.error("Exception occurs while adding standard grading for institute {}, academicSessionId {}", instituteId,
					academicSessionId, e);
		}
		return true;
	}

	public boolean bulkStandardGradingScheme(int instituteId, int academicSessionId, GradeUpdatePayload gradeUpdatePayload) {
		try {
			final Boolean status = transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus status) {
					final List<Object[]> batchInsertArgs = new ArrayList<>();
					int count = 0;
					List<CourseType> courseTypesList = new ArrayList<>(gradeUpdatePayload.getCourseTypeExaminationGradesMap().keySet());
					if (!deleteExaminationGradesStandardWise(academicSessionId, courseTypesList, gradeUpdatePayload.getStandardIdList())) {
						logger.error("Cannot delete the standard grading scheme for institute {}, academicSessionId {}", instituteId,
								academicSessionId);
						throw new EmbrateRunTimeException("Cannot delete the standard grading scheme.");
					}
					for (UUID standardId : gradeUpdatePayload.getStandardIdList()) {
						for (CourseType courseType : courseTypesList) {
							List<StandardExaminationGrades> standardExaminationGradesList = gradeUpdatePayload.getCourseTypeExaminationGradesMap().get(courseType);
							if (CollectionUtils.isEmpty(standardExaminationGradesList)) {
								continue;
							}
							for (StandardExaminationGrades standardExaminationGrades : standardExaminationGradesList) {
								final List<Object> args = new ArrayList<>();
								args.add(instituteId);
								args.add(academicSessionId);
								args.add(standardId.toString());
								args.add(courseType.toString());
								args.add(standardExaminationGrades.getGradeName());
								args.add(standardExaminationGrades.getGradeValue());
								args.add(standardExaminationGrades.getMarksRangeStart());
								args.add(standardExaminationGrades.getMarksRangeEnd());
								args.add(standardExaminationGrades.getRangeDisplayName());
								args.add(standardExaminationGrades.getRemarks());
								args.add(standardExaminationGrades.getCreditScore());

								count++;
								batchInsertArgs.add(args.toArray());
							}
						}
					}

					final int[] rows = jdbcTemplate.batchUpdate(CREATE_EXAM_GRADES, batchInsertArgs);
					if (rows.length != count) {
						throw new EmbrateRunTimeException("Failed to insert all standard grading schemes. Some grades might be missing or incorrectly formatted.");
					}
					for (final int rowCount : rows) {
						if (rowCount != 1) {
							throw new EmbrateRunTimeException("Failed to insert all standard grading schemes. Some grades might be missing or incorrectly formatted.");
						}
					}
					return true;
				}
			});
			return status.booleanValue();
		} catch (final Exception e) {
			logger.error("Exception occurs while adding standard grading scheme for institute {}, academicSessionId {}", instituteId,
					academicSessionId, e);
			throw new EmbrateRunTimeException("Exception occurs while adding standard grading scheme.");
		}
	}

	public boolean addBulkStandardGradingScheme(int instituteId, int academicSessionId, List<UUID> standardIdsList, ExaminationGradesPayloadReadable examinationGradesPayloadReadable) {
		try {
			final Boolean status = transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus status) {
					final List<Object[]> batchInsertArgs = new ArrayList<>();
					int count = 0;
					if (!deleteExaminationGradesStandardWise(academicSessionId, examinationGradesPayloadReadable.getCourseType(), standardIdsList)) {
						logger.error("Cannot delete the standard grading scheme for institute {}, academicSessionId {}", instituteId,
								academicSessionId);
						return false;
					}
					for (UUID standardId : standardIdsList) {
						for (CourseType courseType : examinationGradesPayloadReadable.getCourseType()) {
							for (StandardExaminationGrades standardExaminationGrades : examinationGradesPayloadReadable.getStandardExaminationGrades()) {
								final List<Object> args = new ArrayList<>();
								args.add(instituteId);
								args.add(academicSessionId);
								args.add(standardId.toString());
								args.add(courseType.toString());
								args.add(standardExaminationGrades.getGradeName());
								args.add(standardExaminationGrades.getGradeValue());
								args.add(standardExaminationGrades.getMarksRangeStart());
								args.add(standardExaminationGrades.getMarksRangeEnd());
								args.add(standardExaminationGrades.getRangeDisplayName());
								args.add(standardExaminationGrades.getRemarks());
								args.add(standardExaminationGrades.getCreditScore());

								count++;
								batchInsertArgs.add(args.toArray());
							}
						}
					}

					final int[] rows = jdbcTemplate.batchUpdate(CREATE_EXAM_GRADES, batchInsertArgs);
					if (rows.length != count) {
						throw new EmbrateRunTimeException("Failed to insert all standard grading schemes. Some grades might be missing or incorrectly formatted.");
					}
					for (final int rowCount : rows) {
						if (rowCount != 1) {
							throw new EmbrateRunTimeException("Failed to insert all standard grading schemes. Some grades might be missing or incorrectly formatted.");
						}
					}
					return true;
				}
			});
			return status.booleanValue();
		} catch (final Exception e) {
			logger.error("Exception occurs while adding standard grading scheme for institute {}, academicSessionId {}", instituteId,
					academicSessionId, e);
		}
		return false;
	}

	private boolean deleteExaminationGradesStandardWise(int academicSessionId, List<CourseType> courseTypeList, List<UUID> standardIdsList) {
		try {
			List<Object> args = new ArrayList<>();
			args.add(academicSessionId);
			StringBuilder courseTypeQuery = new StringBuilder("(");
			String delimiter = "";
			for (CourseType courseType : courseTypeList) {
				args.add(courseType.toString());
				courseTypeQuery.append(delimiter);
				courseTypeQuery.append(" ?");
				delimiter = ", ";
			}
			courseTypeQuery.append(" )");
			StringBuilder standardIds = new StringBuilder("(");
			delimiter = "";
			for (UUID standardId : standardIdsList) {
				args.add(standardId.toString());
				standardIds.append(delimiter);
				standardIds.append(" ?");
				delimiter = ", ";
			}
			standardIds.append(" )");
			jdbcTemplate.update(String.format(DELETE_EXAMINATION_GRADES_STANDARD_WISE, courseTypeQuery, standardIds), args.toArray());
			return true;
		} catch (final Exception e) {
			logger.error("Error while deleting examination grades", e);
		}
		return false;
	}

	public List<MarksFeedData> getMarksByStandardIdOnlyGrades(int academicSessionId, List<UUID> standardIdsList) {
		try {
			List<Object> args = new ArrayList<>();
			args.add(academicSessionId);
			StringBuilder standardIds = new StringBuilder("(");
			String delimiter = "";
			for (UUID standardId : standardIdsList) {
				args.add(standardId.toString());
				standardIds.append(delimiter);
				standardIds.append(" ?");
				delimiter = ", ";
			}
			standardIds.append(" )");
			return jdbcTemplate.query(String.format(GET_MARKS_BY_STANDARD_ID_ONLY_GRADES, standardIds), args.toArray(), MARKS_FEEDING_MAPPER);
		} catch (final Exception e) {
			logger.error("Error while getting marks of academicSessionId {}", academicSessionId, e);
		}
		return null;
	}

	public List<MarksFeedData> getMarksByStandardId(int academicSessionId, List<UUID> standardIdsList) {
		try {
			List<Object> args = new ArrayList<>();
			args.add(academicSessionId);
			StringBuilder standardIds = new StringBuilder("(");
			String delimiter = "";
			for (UUID standardId : standardIdsList) {
				args.add(standardId.toString());
				standardIds.append(delimiter);
				standardIds.append(" ?");
				delimiter = ", ";
			}
			standardIds.append(" )");
			return jdbcTemplate.query(String.format(GET_MARKS_BY_STANDARD_ID, standardIds), args.toArray(), MARKS_FEEDING_MAPPER);
		} catch (final Exception e) {
			logger.error("Error while getting marks of academicSessionId {}", academicSessionId, e);
		}
		return null;
	}

	public boolean updateExamDimensionMapping(int instituteId, UUID examId,
											  Map<CourseType, List<ExamDimensionValuesPayload>> courseTypeDimensions) {

		try {
			final Boolean status = transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus status) {
					deleteExamDimensionMapping(instituteId, examId);
					final List<Object[]> dimensionBatchArgs = new ArrayList<>();
					for (final Entry<CourseType, List<ExamDimensionValuesPayload>> entry : courseTypeDimensions
							.entrySet()) {
						final CourseType courseType = entry.getKey();
						if (CollectionUtils.isEmpty(entry.getValue())) {
							continue;
						}
						for (final ExamDimensionValuesPayload dimensionValuesPayload : entry.getValue()) {
							dimensionBatchArgs.add(new Object[]{examId.toString(),
									dimensionValuesPayload.getDimensionId(), courseType.name(),
									dimensionValuesPayload.getMaxMarks(), dimensionValuesPayload.getMinMarks(),
									dimensionValuesPayload.getMaxGrade(), dimensionValuesPayload.getMinGrade()});
						}

					}
					final int[] rows = jdbcTemplate.batchUpdate(ADD_EXAM_DIMENSION_MAPPINGS, dimensionBatchArgs);
					return rows.length == dimensionBatchArgs.size();
				}
			});
			return status.booleanValue();
		} catch (final Exception e) {
			logger.error("Error while updating exam dimension for institute {}, exam {}", instituteId, examId, e);
		}
		return false;
	}

	private boolean deleteExamDimensionMapping(int instituteId, UUID examId) {
		try {
			jdbcTemplate.update(DELETE_EXAM_DIMENSION_MAPPING, examId.toString(), instituteId);
			return true;
		} catch (final Exception e) {
			logger.error("Error while deleting exam dimension for institute {}, exam {}", instituteId, examId, e);
		}
		return false;
	}

	public List<MarksFeedData> getMarksByCourseId(int instituteId, UUID studentId, UUID examId, UUID courseId) {
		try {
			return jdbcTemplate.query(GET_MARKS_BY_COURSE_ID,
					new Object[]{studentId.toString(), examId.toString(), courseId.toString()},
					MARKS_FEEDING_MAPPER);
		} catch (final Exception e) {
			logger.error("Error while getting marks of couese {}, exam {}, student {}, institute{}", courseId, examId,
					studentId, instituteId, e);
		}
		return null;
	}

	/**
	 * will only give studentId, examID and courseId
	 * of marks added for a student in a particular session
	 * Doesnot contain dimension values
	 */
	public List<MarksFeedData> getMarksByStudentId(int academicSessionId, UUID studentId) {
		try {
			return jdbcTemplate.query(GET_MARKS_BY_STUDENT_ID,
					new Object[]{academicSessionId, studentId.toString()},
					MARKS_FEEDING_MAPPER);
		} catch (final Exception e) {
			logger.error("Error while getting marks of studentId {}, academicSessionId {}", studentId, academicSessionId, e);
		}
		return null;
	}

	public List<StudentExamMarksDetails> getClassMarks(int instituteId, UUID examId, UUID courseId, Integer sectionId, boolean filterOnBasisOfMarksSubmitted) {
		//by default we r adding relieved students
		return getClassMarks(instituteId, examId, courseId, sectionId, true, filterOnBasisOfMarksSubmitted);
	}

	public List<StudentExamMarksDetails> getClassMarks(int instituteId, UUID examId, UUID courseId, Integer sectionId,
													   boolean addRelievedStudents, boolean filterOnBasisOfMarksSubmitted) {
		if (courseId == null) {
			return getClassMarks(instituteId, examId, sectionId, addRelievedStudents, filterOnBasisOfMarksSubmitted);
		}

		return getClassMarks(instituteId, examId, new HashSet<>(Collections.singletonList(courseId)),
				sectionId, addRelievedStudents, filterOnBasisOfMarksSubmitted);
	}

	/**
	 * Use this method through manager class only as dimension filtering is required
	 * based on configurations
	 */
	public List<StudentExamMarksDetails> getClassMarks(int instituteId, UUID examId, Set<UUID> courseIdSet, Integer sectionId,
													   boolean addRelievedStudents, boolean filterOnBasisOfMarksSubmitted) {
		if (CollectionUtils.isEmpty(courseIdSet)) {
			return getClassMarks(instituteId, examId, sectionId, addRelievedStudents, filterOnBasisOfMarksSubmitted);
		}
		final List<Object> args = new ArrayList<>();

		args.add(examId.toString());
		String courseQuery = " and exam_courses_assignment.course_id in ";
		String marksFeedStatus = "";
		if(filterOnBasisOfMarksSubmitted){
			marksFeedStatus = " and marks_feeding.status = 'SUBMITTED'";
		}
		StringBuilder inQueryCourses = new StringBuilder();
		inQueryCourses.append("(");
		boolean first = true;
		for (final UUID courseId : courseIdSet) {
			args.add(courseId.toString());
			if (first) {
				inQueryCourses.append("?");
				first = false;
				continue;
			}
			inQueryCourses.append(", ?");
		}
		inQueryCourses.append(")");

		if ((sectionId != null) && (sectionId > 0)) {
			args.add(sectionId);
		}

		final StringBuilder inQueryStudentStatus = new StringBuilder();
		inQueryStudentStatus.append("(");
		inQueryStudentStatus.append("'ENROLLED'");
		if (addRelievedStudents) {
			inQueryStudentStatus.append(", 'RELIEVED'");
		}
		inQueryStudentStatus.append(")");

		try {
			return ExamMarksDetailsRowMapper.getExamMarksDetails(jdbcTemplate
					.query(String.format(GET_EXAM_COURSE_MARKS_DETAILS, marksFeedStatus, inQueryStudentStatus,
									courseQuery, inQueryCourses.toString(),
									(sectionId != null) && (sectionId > 0)
											? " and student_academic_session_details.section_id = ? "
											: ""),
							args.toArray(), EXAM_MARKS_DETAILS_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Error while getting class marks instituteId {}, examId {}, sectionId {}",
					instituteId, examId, sectionId, e);
		}
		return null;
	}

	public List<StudentExamMarksDetails> getClassMarks(int instituteId, UUID examId, Integer sectionId, boolean filterOnBasisOfMarksSubmitted) {
		//by default we r adding relieved students
		return getClassMarks(instituteId, examId, sectionId, true, filterOnBasisOfMarksSubmitted);
	}

	public List<StudentExamMarksDetails> getClassMarks(int instituteId, UUID examId, Integer sectionId,
													   boolean addRelievedStudents, boolean filterOnBasisOfMarksSubmitted) {
		try {
			final List<Object> args = new ArrayList<>();
			args.add(examId.toString());
			if ((sectionId != null) && (sectionId > 0)) {
				args.add(sectionId);
			}
			String marksFeedStatus = "";
			if(filterOnBasisOfMarksSubmitted){
				marksFeedStatus = " and marks_feeding.status = 'SUBMITTED'";
			}
			final StringBuilder inQueryStudentStatus = new StringBuilder();
			inQueryStudentStatus.append("(");
			inQueryStudentStatus.append("'ENROLLED'");
			if (addRelievedStudents) {
				inQueryStudentStatus.append(", 'RELIEVED'");
			}
			inQueryStudentStatus.append(")");

			return ExamMarksDetailsRowMapper
					.getExamMarksDetails(jdbcTemplate.query(String.format(GET_EXAM_COURSE_MARKS_DETAILS, marksFeedStatus,
									inQueryStudentStatus, "", "",
									(sectionId != null) && (sectionId > 0)
											? " and student_academic_session_details.section_id = ? "
											: ""),
							args.toArray(), EXAM_MARKS_DETAILS_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Error while getting class marka for institute {}, examId {}, sectionId {}", instituteId,
					examId, sectionId, e);
		}
		return null;
	}

	public boolean feedMarks(List<MarksFeedData> marksFeedDatas, MarksFeedStatus marksFeedStatus) {
		if (CollectionUtils.isEmpty(marksFeedDatas)) {
			return false;
		}
		try {
			final Boolean status = transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus status) {
					final List<Object[]> deleteArgs = new ArrayList<>();
					final List<Object[]> addNewMarksArgs = new ArrayList<>();
					for (final MarksFeedData marksFeedData : marksFeedDatas) {
						for (final ExamDimensionObtainedValues examDimensionObtainedValues : marksFeedData
								.getExamDimensionObtainedValues()) {
							deleteArgs.add(new Object[]{marksFeedData.getStudentId().toString(),
									marksFeedData.getExamId().toString(), marksFeedData.getCourseId().toString(),
									examDimensionObtainedValues.getExamDimension().getDimensionId()});

							if ((examDimensionObtainedValues.getObtainedMarks() == null)
									&& (ExamGrade.emptyGrade(examDimensionObtainedValues.getObtainedGrade()))
									&& examDimensionObtainedValues.getAttendanceStatus() == null) {
								continue;
							}
							addNewMarksArgs.add(new Object[]{marksFeedData.getStudentId().toString(),
									marksFeedData.getExamId().toString(), marksFeedData.getCourseId().toString(),
									examDimensionObtainedValues.getExamDimension().getDimensionId(),
									examDimensionObtainedValues.getObtainedMarks(),
									ExamGrade.emptyGrade(examDimensionObtainedValues.getObtainedGrade()) ? null
											: examDimensionObtainedValues.getObtainedGrade().getGradeId(),
									examDimensionObtainedValues.getGraceMarks(),
									examDimensionObtainedValues.getAttendanceStatus() == null ? null :
											examDimensionObtainedValues.getAttendanceStatus().name(),
									marksFeedStatus.name()});
						}
					}
					jdbcTemplate.batchUpdate(DELETE_FEED_MARKS, deleteArgs);
					if (jdbcTemplate.batchUpdate(FEED_MARKS, addNewMarksArgs).length != addNewMarksArgs.size()) {
						throw new EmbrateRunTimeException("Unable to update marks");
					}
					return true;
				}
			});
			return status.booleanValue();
		} catch (final Exception e) {
			logger.error("Eror while feeding marks", e);
		}
		return false;
	}

	public Boolean createDefaultExamForStandards(List<ExamCreationPayload> examCreationPayloads) {
		if (CollectionUtils.isEmpty(examCreationPayloads)) {
			return false;
		}
		try {
			final Boolean status = transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus status) {
					for (final ExamCreationPayload examCreationPayload : examCreationPayloads) {
						createExam(examCreationPayload);
					}
					return true;
				}
			});
			return status.booleanValue();
		} catch (final Exception e) {
			logger.error("Error while creatinf exam for standrad", e);
		}
		return false;
	}

	public boolean updateExamReportStructure(int instituteId, ExamReportCardConfiguration examReportCardConfiguration) {
		try {
			final String structure = GSON.toJson(examReportCardConfiguration.getExamReportStructure());
			int rows = jdbcTemplate.update(UPSERT_REPORT_STRUCTURE, instituteId,
					examReportCardConfiguration.getExamReportCardMetadata().getAcademicSessionId(), UUIDUtils.generateUUID().toString(),
					examReportCardConfiguration.getExamReportCardMetadata().getStandardId().toString(),
					examReportCardConfiguration.getExamReportCardMetadata().getReportCardType(),
					examReportCardConfiguration.getExamReportCardMetadata().getReportCardName(), structure,
					examReportCardConfiguration.getExamReportCardMetadata().getReportCardName(), structure);
//			Even though it reports "2 rows affected," in reality, only one row (inserted or updated) is impacted in the table. This is a MySQL-specific quirk and does not mean two distinct rows were touched.
			return rows > 0;
		} catch (final Exception e) {
			logger.error("Error while added exam structure for institute {}, examReportCardConfiguration {}",
					instituteId, examReportCardConfiguration.getExamReportCardMetadata(), e);
		}
		return false;
	}

	public ExamReportCardConfiguration getExamReportCardConfiguration(int instituteId, int academicSessionId,
																	  UUID standardId, String reportType) {
		try {
			return jdbcTemplate.queryForObject(GET_REPORT_STRUCTURE,
					new Object[]{instituteId, academicSessionId, standardId.toString(), reportType},
					EXAM_REPORT_STRUCTURE_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Error while fetching exam structure for institute {}, session {}, standard {}, reportType {}",
					instituteId, academicSessionId, standardId, reportType, e);
		}
		return null;
	}

	public boolean deleteExamReportCardConfiguration(int instituteId, int academicSessionId,
													 UUID standardId, String reportType) {
		try {
			jdbcTemplate.update(DELETE_REPORT_STRUCTURE, instituteId, academicSessionId, standardId.toString(), reportType);
			return true;
		} catch (final Exception e) {
			logger.error("Error while deleting report structure for institute {}, session {}, standard {}, reportType {}",
					instituteId, academicSessionId, standardId, reportType, e);
		}
		return false;
	}

	public List<ExamReportCardConfiguration> getExamReportCardConfigurationList(int instituteId, int academicSessionId,
																				String reportType) {
		try {
			return jdbcTemplate.query(GET_ALL_REPORT_STRUCTURE,
					new Object[]{instituteId, academicSessionId, reportType},
					EXAM_REPORT_STRUCTURE_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Error while fetching exam structure for institute {}, session {}, standard {}, reportType {}",
					instituteId, academicSessionId, reportType, e);
		}
		return null;
	}

	public List<ExamReportCardMetadata> getExamReportCardMetadatas(int instituteId, int academicSessionId,
																   UUID standardId) {
		try {
			return jdbcTemplate.query(GET_REPORT_STRUCTURE_TYPES,
					new Object[]{instituteId, academicSessionId, standardId.toString()},
					EXAM_REPORT_CARD_TYPE_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error(
					"Error while fetching exam report card types for institute {}, session {}, standard {}, reportType {}",
					instituteId, academicSessionId, standardId, e);
		}
		return null;
	}

	public List<ExamReportCardMetadata> getExamReportCardMetadatas(int instituteId, int academicSessionId) {
		try {
			return jdbcTemplate.query(GET_ALL_REPORT_STRUCTURE_TYPES,
					new Object[]{instituteId, academicSessionId},
					EXAM_REPORT_CARD_TYPE_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error(
					"Error while fetching exam report card types for institute {}, session {}",
					instituteId, academicSessionId, e);
		}
		return null;
	}

	public boolean liteUpdateReportCardAttribute(ReportCardVariableDetailsPayloadLite reportCardVariableDetailsLite) {


		boolean isAttendedDaysThere = reportCardVariableDetailsLite.getReportCardVariableParametersSet()
				.contains(ReportCardVariableParameters.ATTENDED_DAYS);
		boolean isRemarksThere = reportCardVariableDetailsLite.getReportCardVariableParametersSet()
				.contains(ReportCardVariableParameters.REMARKS);
		boolean isPrincipalRemarksThere = reportCardVariableDetailsLite.getReportCardVariableParametersSet()
				.contains(ReportCardVariableParameters.PRINCIPAL_REMARKS);
		boolean isHeightThere = reportCardVariableDetailsLite.getReportCardVariableParametersSet()
				.contains(ReportCardVariableParameters.HEIGHT);
		boolean isWeightThere = reportCardVariableDetailsLite.getReportCardVariableParametersSet()
				.contains(ReportCardVariableParameters.WEIGHT);
		boolean isTotalDaysThere = reportCardVariableDetailsLite.getReportCardVariableParametersSet()
				.contains(ReportCardVariableParameters.TOTAL_DAYS);
		boolean isDateOfResultDeclarationThere = reportCardVariableDetailsLite.getReportCardVariableParametersSet()
				.contains(ReportCardVariableParameters.DATE_OF_RESULT_DECLARATION);

		StringBuilder insertQuery = new StringBuilder();
		StringBuilder valueQuery = new StringBuilder();
		StringBuilder updateQuery = new StringBuilder();
		insertQuery.append("insert into report_card_variables(institute_id, academic_session_id, report_type, student_id ");
		valueQuery.append("values (?, ?, ?, ? ");
		updateQuery.append("on duplicate key update ");
		boolean first = true;
		if (isAttendedDaysThere) {
			insertQuery.append(", attended_days ");
			valueQuery.append(", ? ");
			if (first) {
				updateQuery.append(" attended_days =? ");
			} else {
				updateQuery.append(", attended_days =? ");
			}
			first = false;
		}
		if (isRemarksThere) {
			insertQuery.append(", remarks ");
			valueQuery.append(", ? ");
			if (first) {
				updateQuery.append(" remarks = ? ");
			} else {
				updateQuery.append(", remarks = ? ");
			}
			first = false;
		}
		if (isPrincipalRemarksThere) {
			insertQuery.append(", principal_remarks ");
			valueQuery.append(", ? ");
			if (first) {
				updateQuery.append(" principal_remarks = ? ");
			} else {
				updateQuery.append(", principal_remarks = ? ");
			}
			first = false;
		}
		if (isHeightThere) {
			insertQuery.append(", height ");
			valueQuery.append(", ? ");
			if (first) {
				updateQuery.append(" height = ? ");
			} else {
				updateQuery.append(", height = ? ");
			}
			first = false;
		}
		if (isWeightThere) {
			insertQuery.append(", weight ");
			valueQuery.append(", ? ");
			if (first) {
				updateQuery.append(" weight = ? ");
			} else {
				updateQuery.append(", weight = ? ");
			}
			first = false;
		}
		if (isTotalDaysThere) {
			insertQuery.append(", working_days ");
			valueQuery.append(", ? ");
			if (first) {
				updateQuery.append(" working_days = ? ");
			} else {
				updateQuery.append(", working_days = ? ");
			}
			first = false;
		}
		if (isDateOfResultDeclarationThere) {
			insertQuery.append(", date_of_result_declaration ");
			valueQuery.append(", ? ");
			if (first) {
				updateQuery.append(" date_of_result_declaration = ? ");
			} else {
				updateQuery.append(", date_of_result_declaration = ? ");
			}
			first = false;
		}
		insertQuery.append(" ) ");
		valueQuery.append(" ) ");
		StringBuilder query = new StringBuilder();
		query.append(insertQuery).append(valueQuery).append(updateQuery);

		final List<Object[]> batchInsertArgs = new ArrayList<>();
		int count = 0;
		for (StudentReportCardVariableDetailsPayload studentReportCardVariableDetailsPayload : reportCardVariableDetailsLite.getStudentReportCardVariableDetailsPayload()) {
			final List<Object> args = new ArrayList<>();

			args.add(reportCardVariableDetailsLite.getInstituteId());
			args.add(reportCardVariableDetailsLite.getAcademicSessionId());
			args.add(reportCardVariableDetailsLite.getReportType());
			args.add(studentReportCardVariableDetailsPayload.getStudentId().toString());

			//for add part of query
			if (isAttendedDaysThere) {
				args.add(studentReportCardVariableDetailsPayload.getAttendedDays());
			}

			if (isRemarksThere) {
				args.add(StringUtils.isBlank(studentReportCardVariableDetailsPayload.getRemarks()) ? null : studentReportCardVariableDetailsPayload.getRemarks());
			}

			if (isPrincipalRemarksThere) {
				args.add(StringUtils.isBlank(studentReportCardVariableDetailsPayload.getPrincipalRemarks()) ? null : studentReportCardVariableDetailsPayload.getPrincipalRemarks());
			}

			if (isHeightThere) {
				args.add(StringUtils.isBlank(studentReportCardVariableDetailsPayload.getHeight()) ? null : studentReportCardVariableDetailsPayload.getHeight());
			}

			if (isWeightThere) {
				args.add(StringUtils.isBlank(studentReportCardVariableDetailsPayload.getWeight()) ? null : studentReportCardVariableDetailsPayload.getWeight());
			}

			if (isTotalDaysThere) {
				args.add(studentReportCardVariableDetailsPayload.getTotalDays());
			}

			if (isDateOfResultDeclarationThere) {
				args.add(studentReportCardVariableDetailsPayload.getDateOfResultDeclaration() == null ? null
						: new Timestamp(studentReportCardVariableDetailsPayload.getDateOfResultDeclaration() * 1000L));
			}

			//for update part of query
			if (isAttendedDaysThere) {
				args.add(studentReportCardVariableDetailsPayload.getAttendedDays());
			}

			if (isRemarksThere) {
				args.add(StringUtils.isBlank(studentReportCardVariableDetailsPayload.getRemarks()) ? null : studentReportCardVariableDetailsPayload.getRemarks());
			}

			if (isPrincipalRemarksThere) {
				args.add(StringUtils.isBlank(studentReportCardVariableDetailsPayload.getPrincipalRemarks()) ? null : studentReportCardVariableDetailsPayload.getPrincipalRemarks());
			}

			if (isHeightThere) {
				args.add(StringUtils.isBlank(studentReportCardVariableDetailsPayload.getHeight()) ? null : studentReportCardVariableDetailsPayload.getHeight());
			}

			if (isWeightThere) {
				args.add(StringUtils.isBlank(studentReportCardVariableDetailsPayload.getWeight()) ? null : studentReportCardVariableDetailsPayload.getWeight());
			}

			if (isTotalDaysThere) {
				args.add(studentReportCardVariableDetailsPayload.getTotalDays());
			}

			if (isDateOfResultDeclarationThere) {
				args.add(studentReportCardVariableDetailsPayload.getDateOfResultDeclaration() == null ? null
						: new Timestamp(studentReportCardVariableDetailsPayload.getDateOfResultDeclaration() * 1000L));
			}

			count++;
			batchInsertArgs.add(args.toArray());
		}

		try {
			final int[] rows = jdbcTemplate.batchUpdate(query.toString(), batchInsertArgs);
			return rows.length == count;
		} catch (final Exception e) {
			logger.error("Unable to add report card variable details for institute {}", reportCardVariableDetailsLite.getInstituteId(), e);
		}
		return false;
	}
//	public boolean updateReportCardAttribute(ReportCardVariableDetailsPayload reportCardVariableDetails) {
//		try {
//			final Boolean status = transactionTemplate.execute(new TransactionCallback<Boolean>() {
//				@Override
//				public Boolean doInTransaction(TransactionStatus status) {
//					final List<Object[]> reportCardVariablesBatchArgs = new ArrayList<>();
//					for (final StudentReportCardVariableDetailsPayload studentReportCardVariableDetails : reportCardVariableDetails
//							.getStudentReportCardVariableDetailsPayload()) {
//
//						if (studentReportCardVariableDetails.getStudentId() != null) {
//							reportCardVariablesBatchArgs.add(new Object[] { reportCardVariableDetails.getInstituteId(),
//									reportCardVariableDetails.getAcademicSessionId(),
//									reportCardVariableDetails.getReportType(),
//									studentReportCardVariableDetails.getStudentId().toString(),
//									studentReportCardVariableDetails.getAttendedDays(),
//									studentReportCardVariableDetails.getTotalDays(),
//									StringUtils.isBlank(studentReportCardVariableDetails.getRemarks()) ? null
//											: studentReportCardVariableDetails.getRemarks(),
//									StringUtils.isBlank(studentReportCardVariableDetails.getPrincipalRemarks()) ? null
//											: studentReportCardVariableDetails.getPrincipalRemarks(),
//									StringUtils.isBlank(studentReportCardVariableDetails.getHeight()) ? null
//											: studentReportCardVariableDetails.getHeight(),
//									StringUtils.isBlank(studentReportCardVariableDetails.getWeight()) ? null
//											: studentReportCardVariableDetails.getWeight(),
//									studentReportCardVariableDetails.getDateOfResultDeclaration() == null ? null
//											: new Timestamp(studentReportCardVariableDetails.getDateOfResultDeclaration() * 1000l),
//									studentReportCardVariableDetails.getAttendedDays(),
//									studentReportCardVariableDetails.getTotalDays(),
//									StringUtils.isBlank(studentReportCardVariableDetails.getRemarks()) ? null
//											: studentReportCardVariableDetails.getRemarks(),
//									StringUtils.isBlank(studentReportCardVariableDetails.getPrincipalRemarks()) ? null
//											: studentReportCardVariableDetails.getPrincipalRemarks(),
//									StringUtils.isBlank(studentReportCardVariableDetails.getHeight()) ? null
//											: studentReportCardVariableDetails.getHeight(),
//									StringUtils.isBlank(studentReportCardVariableDetails.getWeight()) ? null
//											: studentReportCardVariableDetails.getWeight(),
//									studentReportCardVariableDetails.getDateOfResultDeclaration() == null ? null
//											: new Timestamp(studentReportCardVariableDetails.getDateOfResultDeclaration() * 1000l)});
//						}
//					}
//					final int[] rows = jdbcTemplate.batchUpdate(UPSERT_REPORT_CARD_ATTRIBUTES,
//							reportCardVariablesBatchArgs);
//					return rows.length == reportCardVariablesBatchArgs.size();
//				}
//			});
//			return status.booleanValue();
//		} catch (final Exception e) {
//			logger.error("Error while updating report card attribute", e);
//		}
//		return false;
//	}

//	public List<ReportCardVariablesData> getStudentsReportCardVariables(int instituteId, int academicSessionId,
//			String reportType) {
//		try {
//			return jdbcTemplate.query(GET_REPORT_CARD_VARIABLES,
//					new Object[] { instituteId, academicSessionId, reportType }, REPORT_CARD_VARIABLES_ROW_MAPPER);
//		} catch (final Exception e) {
//			e.printStackTrace();
//		}
//		return null;
//	}

	public StandardMetadata getStandardMetaData(int instituteId, UUID examId) {
		try {
			final Object[] args = {instituteId, examId.toString()};
			return jdbcTemplate.queryForObject(GET_STANDARD_META_DATA, args, STANDARD_META_DATA_ROW_MAPPER);
		} catch (final DataAccessException e) {
			logger.warn("No meta data exists for institute {}, examId {}", instituteId, examId);
		} catch (final Exception e) {
			logger.error("Error occurred while getting standard meta data for institute {}, examId {}", instituteId,
					examId, e);
			throw new EmbrateRunTimeException("Error occurred while getting standard meta data ", e);
		}
		return null;
	}

	public boolean updateGradeScheme(Map<Integer, StandardExaminationGrades> standardExaminationGradeMap) {
		try {
			final List<Object[]> batchInsertArgs = new ArrayList<>();
			int count = 0;
			for (Map.Entry<Integer, StandardExaminationGrades> entry : standardExaminationGradeMap.entrySet()) {
				final List<Object> args = new ArrayList<>();
				Integer gradeId = entry.getKey();
				StandardExaminationGrades gradeDetails = entry.getValue();

				args.add(gradeDetails.getGradeName());
				args.add(gradeDetails.getRangeDisplayName());
				args.add(gradeDetails.getRemarks());
				args.add(gradeDetails.getCreditScore());
				args.add(gradeId);

				count++;
				batchInsertArgs.add(args.toArray());
			}
			final int[] rows = jdbcTemplate.batchUpdate(UPDATE_GRADE_DETAILS, batchInsertArgs);
			if (rows.length != count) {
				return false;
			}
			for (final int rowCount : rows) {
				if (rowCount != 1) {
					return false;
				}
			}
			return true;
		} catch (final Exception e) {
			logger.error("Exception occurs while updating standard grading scheme", e);
		}
		return false;
	}

	public boolean deleteStandardGradingScheme(int instituteId, int academicSessionId, List<UUID> standardIdsList) {
		try {
			StringBuilder query = new StringBuilder(DELETE_EXAM_GRADES);
			List<Object> args = new ArrayList<>();
			args.add(instituteId);
			args.add(academicSessionId);
			if (CollectionUtils.isEmpty(standardIdsList)) {
				throw new RuntimeException(
						"StandardId List can't be empty");
			}
			query.append(" AND standard_id IN (");
			query.append(PlaceholdersUtils.buildPlaceholders(standardIdsList.size()));
			query.append(")");
			for (UUID standardId : standardIdsList) {
				args.add(standardId.toString());
			}
			return jdbcTemplate.update(query.toString(), args.toArray()) > 0;

		} catch (final Exception e) {
			logger.error(
					"Error occured while getting grades for institute {}, academicSessionId {}, standardId {}",
					instituteId, academicSessionId, standardIdsList, e);
		}
		return false;
	}

	public List<ExamGrade> getExamGrades(int instituteId, int academicSessionId, UUID standardId,
										 CourseType courseType) {
		try {
			StringBuilder query = new StringBuilder(GET_GRADES_STRING);
			List<Object> args = new ArrayList<>();
			args.add(instituteId);
			args.add(academicSessionId);
			if (standardId != null) {
				query.append(" and standard_id = ? ");
				args.add(standardId.toString());
			}
			if (courseType != null) {
				query.append("and course_type = ? ");
				args.add(courseType.name());
			}
			query.append(" order by grade_value desc");

			return jdbcTemplate.query(query.toString(), args.toArray(), EXAM_GRADE_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error(
					"Error occured while getting grades for institute {}, academicSessionId {}, standardId {}, courseType {}",
					instituteId, academicSessionId, standardId, courseType, e);
		}
		return null;
	}

	public Map<CourseType, List<ExamGrade>> getExamGrades(int instituteId, int academicSessionId, UUID standardId) {
		try {
			final Object[] args = {instituteId, academicSessionId, standardId.toString()};
			final List<ExamGrade> examGrades = jdbcTemplate.query(GET_GRADES_BY_CLASS, args, EXAM_GRADE_ROW_MAPPER);
			if (CollectionUtils.isEmpty(examGrades)) {
				return new HashMap<>();
			}
			final Map<CourseType, List<ExamGrade>> courseTypeExamGrades = new HashMap<>();
			for (final ExamGrade examGrade : examGrades) {
				if (!courseTypeExamGrades.containsKey(examGrade.getCourseType())) {
					courseTypeExamGrades.put(examGrade.getCourseType(), new ArrayList<ExamGrade>());
				}
				courseTypeExamGrades.get(examGrade.getCourseType()).add(examGrade);
			}
			return courseTypeExamGrades;
		} catch (final Exception e) {
			logger.error("Error occured while getting grades for institute {}, academicSessionId {}, standardId {}",
					instituteId, academicSessionId, standardId, e);
		}
		return null;
	}

	public List<ExamGrade> getExamGrades(int instituteId, UUID examId, CourseType courseType) {
		try {
			final Object[] args = {instituteId, examId.toString(), courseType.name()};
			return jdbcTemplate.query(GET_GRADES_BY_EXAM_AND_COURSE_TYPE, args, EXAM_GRADE_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Error occurred while getting grades for institute {}, examId {}, courseType {}", instituteId,
					examId, courseType, e);
		}
		return null;
	}

	public boolean addExamGrades(int instituteId, int academicSessionId, List<ExamGrade> examGradeList) {
		try {
			List<Object[]> args = new ArrayList<>();
			for (ExamGrade examGrade : examGradeList) {
				args.add(new Object[]{instituteId, academicSessionId, examGrade.getStandardId().toString(),
						examGrade.getCourseType().name(), examGrade.getGradeName().trim(), examGrade.getGradeValue(),
						examGrade.getMarksRangeStart(), examGrade.getMarksRangeEnd(), examGrade.getRangeDisplayName(), examGrade.getRemarks(), examGrade.getCreditScore()});
			}
			return jdbcTemplate.batchUpdate(CREATE_EXAM_GRADES, args).length == args.size();
		} catch (final Exception e) {
			logger.error("Error occurred while adding exam grades for institute {}, academicSessionId {} ", instituteId,
					academicSessionId, e);
		}
		return false;
	}


	public ReportCardVariableDetails getReportCardVariablesByReportType(int instituteId, UUID standardId,
																		Integer sectionId, int academicSessionId, String reportType, UUID studentId) {
		try {
			String query = GET_REPORT_CARD_VARIABLES_BY_REPORT_TYPE;
			final List<Object> args = new ArrayList<>();
			args.add(reportType);
			args.add(standardId.toString());
			args.add(instituteId);
			args.add(academicSessionId);
			String inQuerySection = "";
			if ((sectionId != null) && (sectionId > 0)) {
				args.add(sectionId);
				inQuerySection = " and student_academic_session_details.section_id = ? ";
			}

			String inQueryStudent = "";
			if (studentId != null) {
				args.add(studentId.toString());
				inQueryStudent = " and students.student_id = ? ";
			}

			return ReportCardVariableDetailsRowMapper.getReportCardVariableDetailsByReportType(
					jdbcTemplate.query(String.format(query, inQuerySection, inQueryStudent),
							args.toArray(), REPORT_CARD_VARIABLE_DETAILS_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Error while getting report card attributs", e);
		}
		return null;
	}

	public boolean addGreenSheetStructure(
			GreenSheetClassStructure<GreenSheetExamDimensionMapData> greenSheetClassStructure) {
		try {
			String structure = GSON.toJson(greenSheetClassStructure.getGreenSheetStructureData());
			return jdbcTemplate.update(UPSERT_GREEN_SHEET_EXAM_STRUCTURE, greenSheetClassStructure.getInstituteId(),
					greenSheetClassStructure.getAcademicSessionId(),
					greenSheetClassStructure.getStandardId().toString(), structure, structure) > 0;
		} catch (Exception e) {
			logger.error("Error while adding green sheet structure {}, {}, {}",
					greenSheetClassStructure.getInstituteId(), greenSheetClassStructure.getAcademicSessionId(),
					greenSheetClassStructure.getStandardId().toString(), e);
		}
		return false;
	}

	public GreenSheetClassStructure<GreenSheetExamDimensionMapData> getGreenSheetStructure(int instituteId,
																						   int academicSessionId, UUID standardId) {
		try {
			return jdbcTemplate.queryForObject(GET_GREEN_SHEET_EXAM_STRUCTURE,
					new Object[]{instituteId, academicSessionId, standardId.toString()},
					EXAM_GREEN_SHEET_STRUCTURE_ROW_MAPPER);
		} catch (Exception e) {
			logger.error("Error while getting green sheet structure {}, {}, {}", instituteId, academicSessionId,
					standardId, e);
		}
		return null;
	}

	public boolean addDatesheet(DatesheetAndSyllabusPayload datesheetAndSyllabusPayload, UUID userId) {
		try {
			datesheetAndSyllabusPayload.setDatesheetId(UUID.randomUUID());
			final Boolean savedDatesheet = transactionTemplate.execute(new TransactionCallback<Boolean>() {

				@Override
				public Boolean doInTransaction(TransactionStatus status) {

					final List<Object> args = new ArrayList<>();
					args.add(datesheetAndSyllabusPayload.getInstituteId());
					args.add(datesheetAndSyllabusPayload.getAcademicSessionId());
					args.add(datesheetAndSyllabusPayload.getDatesheetId().toString());
					args.add(datesheetAndSyllabusPayload.getStandardId().toString());
					args.add(datesheetAndSyllabusPayload.getSectionId());
					args.add(datesheetAndSyllabusPayload.getExamId().toString());
					args.add(new Timestamp(datesheetAndSyllabusPayload.getExamStartDate() * 1000l));
					args.add(new Timestamp(datesheetAndSyllabusPayload.getExamEndDate() * 1000l));
					args.add(datesheetAndSyllabusPayload.getDatesheetStatus().name());
					args.add(userId.toString());
					args.add(datesheetAndSyllabusPayload.getNotes());
					if (jdbcTemplate.update(ADD_DATESHEET, args.toArray()) != 1) {
						logger.error("Error while adding datesheet");
						return false;
					}

					if (!addDatesheetDetails(datesheetAndSyllabusPayload)) {
						throw new EmbrateRunTimeException("Unable to add datesheet");

					}
					return true;
				}
			});
			return savedDatesheet;
		} catch (final Exception e) {
			logger.error("Error while adding datesheet", e);
		}
		return false;
	}

	public boolean addDatesheetDetails(DatesheetAndSyllabusPayload datesheetAndSyllabusPayload) {
		final List<Object[]> addNewRecordsArgs = new ArrayList<>();

		for (CourseWiseDatesheetPayload courseWiseDatesheetPayload : datesheetAndSyllabusPayload.getCourseWiseDatesheetList()) {
			for (DimensionWiseDatesheetPayload dimensionWiseDatesheetPayload : courseWiseDatesheetPayload.getDimensionWiseDatesheetList()) {

				final List<Object> args = new ArrayList<>();

				args.add(datesheetAndSyllabusPayload.getDatesheetId().toString());
				args.add(courseWiseDatesheetPayload.getCourseId().toString());
				args.add(dimensionWiseDatesheetPayload.getDimensionId());
				args.add(new Timestamp(DateUtils.calculateDateTime(dimensionWiseDatesheetPayload.getCourseExamDate(), dimensionWiseDatesheetPayload.getStartTime()) * 1000l));
				args.add(new Timestamp(DateUtils.calculateDateTime(dimensionWiseDatesheetPayload.getCourseExamDate(), dimensionWiseDatesheetPayload.getEndTime()) * 1000l));
				args.add(dimensionWiseDatesheetPayload.getSyllabus());

				addNewRecordsArgs.add(args.toArray());
			}
		}
		final int[] rows = jdbcTemplate.batchUpdate(ADD_DATESHEET_DETAILS, addNewRecordsArgs);

		if (rows.length != addNewRecordsArgs.size()) {
			return false;
		}

		for (final int rowCount : rows) {
			if (rowCount != 1) {
				return false;
			}
		}
		return true;
	}

	public boolean updateDatesheet(DatesheetAndSyllabusPayload datesheetAndSyllabusPayload, UUID userId) {
		try {
			final Boolean updateDatesheet = transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus status) {

					final boolean deleteDatesheet = jdbcTemplate
							.update(DELETE_FROM_DATESHEET_DETAILS, datesheetAndSyllabusPayload.getDatesheetId().toString()) > 0;
					if (!deleteDatesheet) {
						throw new RuntimeException(
								"Transaction could not delete Datesheet details");
					}

					final List<Object[]> addNewRecordsArgs = new ArrayList<>();

					for (CourseWiseDatesheetPayload courseWiseDatesheetPayload : datesheetAndSyllabusPayload.getCourseWiseDatesheetList()) {
						for (DimensionWiseDatesheetPayload dimensionWiseDatesheetPayload : courseWiseDatesheetPayload.getDimensionWiseDatesheetList()) {

							final List<Object> args = new ArrayList<>();

							args.add(datesheetAndSyllabusPayload.getDatesheetId().toString());
							args.add(courseWiseDatesheetPayload.getCourseId().toString());
							args.add(dimensionWiseDatesheetPayload.getDimensionId());
							args.add(new Timestamp(DateUtils.calculateDateTime(dimensionWiseDatesheetPayload.getCourseExamDate(), dimensionWiseDatesheetPayload.getStartTime()) * 1000l));
							args.add(new Timestamp(DateUtils.calculateDateTime(dimensionWiseDatesheetPayload.getCourseExamDate(), dimensionWiseDatesheetPayload.getEndTime()) * 1000l));
							args.add(dimensionWiseDatesheetPayload.getSyllabus());

							addNewRecordsArgs.add(args.toArray());
						}
					}

					final int[] rows = jdbcTemplate.batchUpdate(ADD_DATESHEET_DETAILS, addNewRecordsArgs);

					if (rows.length != addNewRecordsArgs.size()) {
						return false;
					}

					final List<Object> args = new ArrayList<>();

					args.add(new Timestamp(datesheetAndSyllabusPayload.getExamStartDate() * 1000l));
					args.add(new Timestamp(datesheetAndSyllabusPayload.getExamEndDate() * 1000l));
					args.add(userId.toString());
					args.add(datesheetAndSyllabusPayload.getNotes());
					args.add(datesheetAndSyllabusPayload.getDatesheetId().toString());

					final boolean flag = jdbcTemplate.update(UPDATE_DATESHEET_METADATA, args.toArray()) > 0;

					return deleteDatesheet && flag;
				}
			});
			return updateDatesheet;
		} catch (final Exception e) {
			logger.error("Error while updating datesheet");
		}
		return false;
	}

	public boolean deleteDatesheet(UUID datesheetId) {
		try {
			final Boolean deletedDatesheet = transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus status) {

					final boolean deleteDatesheet = jdbcTemplate.update(DELETE_FROM_DATESHEET_DETAILS, datesheetId.toString()) > 0;

					if (!deleteDatesheet) {
						throw new RuntimeException(
								"Transaction could not delete Datesheet details");
					}

					final boolean flag = jdbcTemplate.update(DELETE_DATESHEET_METADATA, datesheetId.toString()) > 0;

					return deleteDatesheet && flag;
				}
			});
			return deletedDatesheet;
		} catch (final Exception e) {
			logger.error("Error while deleting datesheet");
		}
		return false;
	}

	public List<DatesheetMetadata> getDatesheet(int instituteId, int academic_session_id, UUID standardId) {
		try {
			final List<Object> args = new ArrayList<>();
			args.add(academic_session_id);
			args.add(standardId.toString());
			args.add(academic_session_id);
			args.add(instituteId);
			return DatesheetMetadataRowMapper.getDatesheetMetadataList(
					jdbcTemplate.query(GET_DATESHEET_METADATA, args.toArray(), DATESHEET_METADATA_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Error while getting datesheets");
		}
		return null;
	}

	public DatesheetDetails getDatesheetDetailsByDatesheetId(int instituteId, int academicSessionId, UUID standardId, UUID datesheetId) {
		try {
			final List<Object> args = new ArrayList<>();
			args.add(academicSessionId);
			args.add(instituteId);
			args.add(academicSessionId);
			args.add(standardId.toString());
			args.add(datesheetId.toString());
			return DatesheetDetailsRowMapper.getDatesheet(jdbcTemplate.query(GET_DATESHEET_DETAILS + DATESHEET_ID_CLAUSE,
					args.toArray(), DATESHEET_DETAILS_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Error while getting datesheet details");
		}
		return null;
	}

	public DatesheetDetails getDatesheetDetailsByExamId(int instituteId, int academicSessionId, UUID standardId, UUID examId) {
		try {
			final List<Object> args = new ArrayList<>();
			args.add(academicSessionId);
			args.add(instituteId);
			args.add(academicSessionId);
			args.add(standardId.toString());
			args.add(examId.toString());
			return DatesheetDetailsRowMapper.getDatesheet(jdbcTemplate.query(GET_DATESHEET_DETAILS + EXAM_ID_CLAUSE,
					args.toArray(), DATESHEET_DETAILS_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Error while getting datesheet details");
		}
		return null;
	}

	public Map<Integer, List<DatesheetDetailRow>> getSortedDatesheetDetailRow(int instituteId, int academicSessionId,
																			  UUID standardId, UUID examId, UUID datesheetId) {
		try {
			String query = GET_DATESHEET_DETAILS;
			final List<Object> args = new ArrayList<>();
			args.add(academicSessionId);
			args.add(instituteId);
			args.add(academicSessionId);
			args.add(standardId.toString());
			if (examId != null) {
				query += EXAM_ID_CLAUSE;
				args.add(examId.toString());
			}
			if (datesheetId != null) {
				query += DATESHEET_ID_CLAUSE;
				args.add(datesheetId.toString());
			}

			return DatesheetDetailsRowMapper.getSortedDatesheetDetailRow(jdbcTemplate.query(query,
					args.toArray(), DATESHEET_DETAILS_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Error while getting datesheet details");
		}
		return null;
	}

	public boolean updateExamCoursesStatus(UUID examId, ExamCoursePublishedStatus examCoursePublishedStatus) {
		try {
			List<Object> args = new ArrayList<>();
			args.add(examCoursePublishedStatus.name());
			args.add(examId.toString());
			return jdbcTemplate.update(UPDATE_EXAM_COURSES_STATUS, args.toArray()) > 0;
		} catch (final Exception e) {
			logger.error("Error while updating examIds {}", examId, e);
		}
		return false;

	}

	public boolean updateExamMarksFeedStatus(int academicSessionId, MarksFeedStatus marksFeedStatus, UUID examId, UUID courseId, int dimensionId, Integer sectionId) {
		try {
			if (sectionId == null) {
				return jdbcTemplate.update(UPDATE_FEED_MARKS_STATUS, marksFeedStatus.name(), examId.toString(), courseId.toString(), dimensionId) >= 0;
			}
			return jdbcTemplate.update(UPDATE_FEED_MARKS_STATUS_WITH_SECTION, sectionId, academicSessionId, marksFeedStatus.name(), examId.toString(), courseId.toString(), dimensionId) >= 0;
		} catch (final Exception e) {
			logger.error("Error while updating status for exam {}, course {}, dimension {}", examId, courseId, dimensionId, e);
		}
		return false;

	}

	public boolean addPersonalityTraitsDetails(int instituteId, List<PersonalityTraitsDetails> personalityTraitsDetailList) {
		try {
			final List<Object[]> argsList = new ArrayList<>();
			for (final PersonalityTraitsDetails personalityTraitsDetails : personalityTraitsDetailList) {
				final List<Object> args = new ArrayList<>();
				UUID personalityTraitId = UUID.randomUUID();
				args.add(instituteId);
				args.add(personalityTraitsDetails.getAcademicSessionId());
				args.add(personalityTraitId.toString());
				args.add(personalityTraitsDetails.getPersonalityTraitName());
				args.add(personalityTraitsDetails.getStandardId().toString());
				args.add(personalityTraitsDetails.getPersonalityTraitSequence());
				argsList.add(args.toArray());
			}
			final int[] rows = jdbcTemplate.batchUpdate(ADD_PERSONALITY_TRAITS, argsList);
			return rows.length == personalityTraitsDetailList.size();
		} catch (final Exception e) {
			logger.error("Error while adding the personality traits", e);
		}
		return false;
	}

	public boolean updatePersonalityTraitsDetails(List<PersonalityTraitsDetails> personalityTraitsDetailList) {
		try {
			final List<Object[]> batchUpdateArgs = new ArrayList<>();
			for (final PersonalityTraitsDetails personalityTraitsDetails : personalityTraitsDetailList) {
				final List<Object> args = new ArrayList<>();
				args.add(personalityTraitsDetails.getPersonalityTraitName());
				args.add(personalityTraitsDetails.getPersonalityTraitSequence());
				args.add(personalityTraitsDetails.getPersonalityTraitId().toString());
				batchUpdateArgs.add(args.toArray());
			}
			final int[] rows = jdbcTemplate.batchUpdate(UPDATE_PERSONALITY_TRAITS, batchUpdateArgs);
			return rows.length == personalityTraitsDetailList.size();
		} catch (final Exception e) {
			logger.error("Unable to update personality trait details", e);
		}
		return false;
	}

	public Map<UUID, List<StudentPersonalityTraitUsageInfo>> getStudentPersonalityTraitUsageDetails(int instituteId, int academicSessionId, List<UUID> personalityTraitIds) {
		try {
			if (CollectionUtils.isEmpty(personalityTraitIds)) {
				return new HashMap<>();
			}

			List<Object> args = new ArrayList<>();
			args.add(instituteId);
			args.add(academicSessionId);

			// Create placeholders for IN clause
			StringBuilder placeholders = new StringBuilder();
			for (int i = 0; i < personalityTraitIds.size(); i++) {
				if (i > 0) {
					placeholders.append(", ");
				}
				placeholders.append("?");
				args.add(personalityTraitIds.get(i).toString());
			}

			String query = String.format(GET_STUDENT_PERSONALITY_TRAIT_USAGE_DETAILS, placeholders.toString());
			List<StudentPersonalityTraitUsageInfo> usageInfoList = jdbcTemplate.query(query, args.toArray(), STUDENT_PERSONALITY_TRAIT_USAGE_INFO_ROW_MAPPER);

			// Group by personality trait ID
			Map<UUID, List<StudentPersonalityTraitUsageInfo>> groupedUsage = new HashMap<>();
			for (StudentPersonalityTraitUsageInfo usageInfo : usageInfoList) {
				UUID traitId = usageInfo.getPersonalityTraitId();
				if (!groupedUsage.containsKey(traitId)) {
					groupedUsage.put(traitId, new ArrayList<StudentPersonalityTraitUsageInfo>());
				}
				groupedUsage.get(traitId).add(usageInfo);
			}

			return groupedUsage;
		} catch (final Exception e) {
			logger.error("Error while getting student personality trait usage details for institute {}", instituteId, e);
		}
		return new HashMap<>();
	}

	public boolean deletePersonalityTraitsByIds(int instituteId, List<UUID> personalityTraitIds) {
		try {
			if (CollectionUtils.isEmpty(personalityTraitIds)) {
				return true;
			}

			List<Object> args = new ArrayList<>();
			args.add(instituteId);

			// Create placeholders for IN clause
			StringBuilder placeholders = new StringBuilder();
			for (int i = 0; i < personalityTraitIds.size(); i++) {
				if (i > 0) {
					placeholders.append(", ");
				}
				placeholders.append("?");
				args.add(personalityTraitIds.get(i).toString());
			}

			String query = String.format(DELETE_PERSONALITY_TRAITS_BY_IDS, placeholders.toString());

			int deletedCount = jdbcTemplate.update(query, args.toArray());
			return deletedCount == personalityTraitIds.size();
		} catch (final Exception e) {
			logger.error("Error while deleting personality traits by IDs for institute {}", instituteId, e);
		}
		return false;
	}

	public boolean deletePersonalityTraitsDetails(int instituteId, int academicSessionId, List<UUID> personalityTraitIdList, UUID standardId) {
		try {

			final List<Object[]> batchDeleteArgs = new ArrayList<>();
			for (UUID personalityTraitId : personalityTraitIdList) {
				final List<Object> args = new ArrayList<>();
				args.add(instituteId);
				args.add(academicSessionId);
				args.add(standardId.toString());
				args.add(personalityTraitId.toString());
				batchDeleteArgs.add(args.toArray());
			}
			final int[] rows = jdbcTemplate.batchUpdate(DELETE_PERSONALITY_TRAITS, batchDeleteArgs);
			return rows.length == personalityTraitIdList.size();
		} catch (final Exception e) {
			logger.error("Unable to delete personality trait details for institute {}", instituteId, e);
		}
		return false;
	}

	public List<PersonalityTraitsDetails> getPersonalityTraits(int instituteId, int academicSessionId, UUID standardId) {
		try {
			List<Object> args = new ArrayList<>();
			String query = GET_INSTITUTE_PERSONALITY_TRAITS;
			args.add(instituteId);
			args.add(academicSessionId);
			if (standardId != null) {
				query += STANDARD_WHERE_CLAUSE;
				args.add(standardId.toString());
			}
			return jdbcTemplate.query(query, args.toArray(), PERSONALITY_TRAITS_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Error while getting exam courses", e);
		}
		return null;
	}

	public Map<UUID, List<PersonalityTraitsDetails>> getPersonalityTraitsForMultipleStandards(int instituteId, int academicSessionId, List<UUID> standardIds) {
		try {
			if (CollectionUtils.isEmpty(standardIds)) {
				return new HashMap<>();
			}

			List<Object> args = new ArrayList<>();
			args.add(instituteId);
			args.add(academicSessionId);

			// Create placeholders for IN clause
			StringBuilder placeholders = new StringBuilder();
			for (int i = 0; i < standardIds.size(); i++) {
				if (i > 0) {
					placeholders.append(", ");
				}
				placeholders.append("?");
				args.add(standardIds.get(i).toString());
			}

			String query = String.format(GET_PERSONALITY_TRAITS_FOR_MULTIPLE_STANDARDS, placeholders.toString());
			List<PersonalityTraitsDetails> allTraits = jdbcTemplate.query(query, args.toArray(), PERSONALITY_TRAITS_ROW_MAPPER);

			// Group by standard ID
			Map<UUID, List<PersonalityTraitsDetails>> groupedTraits = new HashMap<>();
			for (PersonalityTraitsDetails trait : allTraits) {
				UUID standardId = trait.getStandardId();
				if (!groupedTraits.containsKey(standardId)) {
					groupedTraits.put(standardId, new ArrayList<PersonalityTraitsDetails>());
				}
				groupedTraits.get(standardId).add(trait);
			}

			return groupedTraits;
		} catch (final Exception e) {
			logger.error("Error while getting personality traits for multiple standards for institute {}", instituteId, e);
		}
		return new HashMap<>();
	}



	public List<StudentPersonalityTraitsDetailsRow> getStudentPersonalityTraits(int instituteId, UUID standardId,
																				Integer sectionId, int academicSessionId, String reportType, UUID studentId) {
		try {
			String query = GET_STUDENT_PERSONALITY_TRAIT_DETAILS;
			final List<Object> args = new ArrayList<>();
			args.add(reportType);
			args.add(standardId.toString());
			args.add(instituteId);
			args.add(academicSessionId);
			String inQuerySection = "";
			if ((sectionId != null) && (sectionId > 0)) {
				args.add(sectionId);
				inQuerySection = " and student_academic_session_details.section_id = ? ";
			}

			String inQueryStudent = "";
			if (studentId != null) {
				args.add(studentId.toString());
				inQueryStudent = " and students.student_id = ? ";
			}
			return jdbcTemplate.query(String.format(query, inQuerySection, inQueryStudent),
					args.toArray(), STUDENT_PERSONALITY_TRAITS_DETAILS_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Error while getting personality traits", e);
		}
		return null;
	}

	public boolean updateStudentPersonalityTraitsDetails(int instituteId, int academicSessionId, String reportType, List<StudentPersonalityTraitsPayload> reportCardVariableDetails) {
		try {
			final List<Object[]> reportCardVariablesBatchArgs = new ArrayList<>();
			for (final StudentPersonalityTraitsPayload studentPersonalityTraitsPayload : reportCardVariableDetails) {
				if (studentPersonalityTraitsPayload.getStudentId() != null && studentPersonalityTraitsPayload.getPersonalityTraitId() != null) {
					reportCardVariablesBatchArgs.add(new Object[]{instituteId,
							academicSessionId,
							studentPersonalityTraitsPayload.getPersonalityTraitId().toString(),
							reportType,
							studentPersonalityTraitsPayload.getStudentId().toString(),
							studentPersonalityTraitsPayload.getRemarks(),
							studentPersonalityTraitsPayload.getRemarks()});
				}
			}
			final int[] rows = jdbcTemplate.batchUpdate(UPSERT_REPORT_CARD_PERSONALITY_TRAITS,
					reportCardVariablesBatchArgs);
			return rows.length > 0;
		} catch (final Exception e) {
			logger.error("Error while updating report card attribute", e);
		}
		return false;
	}

	//	-----Student Report Card Status APIs-----
	public List<StudentReportCardStatusDetails> getStudentReportCardStatusDetails(int instituteId, int academicSessionId, UUID standardId,
																				  Set<Integer> sectionIdSet, UUID reportCardId, UUID studentId) {
		try {
			String query = GET_STUDENT_REPORT_CARD_DETAILS;
			final List<Object> args = new ArrayList<>();
			args.add(academicSessionId);
			args.add(instituteId);
			args.add(academicSessionId);

			final StringBuilder inQueryStandard = new StringBuilder();
			if (standardId != null) {
				args.add(standardId.toString());
				inQueryStandard.append(STANDARD_ID_CLAUSE);
			}

			final StringBuilder inQuerySection = new StringBuilder();
			if (!CollectionUtils.isEmpty(sectionIdSet)) {
				inQuerySection.append(SECTION_CLAUSE);
				inQuerySection.append(" (");
				boolean firstSection = true;
				for (final Integer sectionId : sectionIdSet) {
					if (sectionId == null || sectionId <= 0) {
						continue;
					}
					args.add(sectionId);
					if (firstSection) {
						inQuerySection.append("?");
						firstSection = false;
						continue;
					}
					inQuerySection.append(", ?");
				}
				inQuerySection.append(") ");
			}

			final StringBuilder inQueryReportCard = new StringBuilder();
			if (reportCardId != null) {
				args.add(reportCardId.toString());
				inQueryReportCard.append(REPORT_CARD_ID_CLAUSE);
			}

			final StringBuilder inQueryStudent = new StringBuilder();
			final StringBuilder inQueryReportCardStatus = new StringBuilder();
			if (studentId != null) {
				args.add(studentId.toString());
				inQueryStudent.append(STUDENT_CLAUSE);
				//If studentId != null -> Mobile App Student side where we will only show publish report cards
				inQueryReportCardStatus.append(REPORT_CARD_STATUS_CLAUSE);
			}

			return jdbcTemplate.query(String.format(query, inQueryStandard, inQuerySection, inQueryReportCard, inQueryStudent, inQueryReportCardStatus),
					args.toArray(), STUDENT_REPORT_CARD_STATUS_DETAILS_ROW_MAPPER);
		} catch (final Exception e) {
			e.printStackTrace();
			logger.error("Error while getting personality traits", e);
		}
		return null;
	}

	public boolean updateStudentReportCardStatusPayload(StudentReportCardStatusPayload studentReportCardStatusPayload) {
		try {
			final List<Object[]> studentReportCardStatusBatchArgs = new ArrayList<>();
			for (final UUID studentId : studentReportCardStatusPayload.getStudentIdSet()) {
				studentReportCardStatusBatchArgs.add(new Object[]{studentReportCardStatusPayload.getInstituteId(),
						studentReportCardStatusPayload.getAcademicSessionId(),
						studentReportCardStatusPayload.getReportCardId().toString(), studentId.toString(),
						studentReportCardStatusPayload.getStudentExamDisplayDataStatus().name(),
						studentReportCardStatusPayload.getStudentExamDisplayDataStatus().name()});
			}
			final int[] rows = jdbcTemplate.batchUpdate(UPSERT_STUDENT_REPORT_CARD_MAPPING,
					studentReportCardStatusBatchArgs);
			return rows.length > 0;
		} catch (final Exception e) {
			e.printStackTrace();
			logger.error("Error while updating student report card status", e);
		}
		return false;
	}

	/**
	 * Bulk upsert standards metadata using batch operations
	 *
	 * @param instituteId
	 * @param academicSessionId
	 * @param standardMetadataItems
	 * @return
	 */
	public boolean bulkUpsertStandardsMetadata(int instituteId, int academicSessionId, List<StandardMetadataItem> standardMetadataItems) {
		try {
			final List<Object[]> batchInsertArgs = new ArrayList<>();
			int count = 0;
			for (StandardMetadataItem item : standardMetadataItems) {
				final List<Object> args = new ArrayList<>();
				args.add(instituteId);
				args.add(academicSessionId);
				args.add(item.getStandardId().toString());
				args.add(item.isCoScholasticGradeEnabled());
				args.add(item.isScholasticGradeEnabled());
				args.add(item.isRoundExamReportMarks());
				count++;
				batchInsertArgs.add(args.toArray());
			}

			final int[] rows = jdbcTemplate.batchUpdate(BULK_UPSERT_STANDARDS_METADATA, batchInsertArgs);
			if (rows.length != count) {
				return false;
			}

			// Check that all operations were successful (each should affect exactly 1 row for insert or 2 rows for update)
			for (final int rowCount : rows) {
				if (rowCount <= 0) {
					return false;
				}
			}
			return true;
		} catch (final Exception e) {
			logger.error("Error while bulk upserting standards metadata for institute {}, session {}",
					instituteId, academicSessionId, e);
		}
		return false;
	}

	//	-----Student Report Card Status APIs-----
}
